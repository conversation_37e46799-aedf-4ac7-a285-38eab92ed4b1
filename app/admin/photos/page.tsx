import { createSupabaseServerClient } from "@/lib/supabase/client"
import { redirect } from "next/navigation"

export default async function AdminPhotosPage() {
  const supabase = await createSupabaseServerClient()
  
  // Check if user is authenticated and is admin
  const { data: { user }, error: authError } = await supabase.auth.getUser()
  
  if (authError || !user) {
    redirect("/login")
  }

  // Get user profile to check if admin
  const { data: profile } = await supabase
    .from("users")
    .select("role, email")
    .eq("id", user.id)
    .single()

  // Simple admin check - you can make this more sophisticated
  const isAdmin = profile?.email === '<EMAIL>' // Change this to your email

  if (!isAdmin) {
    redirect("/dashboard")
  }

  // Get flagged photos
  const { data: flaggedPhotos } = await supabase
    .from("photos")
    .select(`
      *,
      diary_entries (
        title,
        users (
          name,
          email
        )
      )
    `)
    .eq("moderation_status", "flagged")
    .order("created_at", { ascending: false })

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Flagged Photos Review
          </h1>

          {!flaggedPhotos || flaggedPhotos.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500">No flagged photos to review</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {flaggedPhotos.map((photo: any) => (
                <div key={photo.id} className="border border-red-200 rounded-lg p-4 bg-red-50">
                  <img
                    src={photo.url}
                    alt={photo.alt_text}
                    className="w-full h-48 object-cover rounded-lg mb-4"
                  />
                  
                  <div className="space-y-2 text-sm">
                    <p><strong>Entry:</strong> {photo.diary_entries?.title}</p>
                    <p><strong>Author:</strong> {photo.diary_entries?.users?.name}</p>
                    <p><strong>Email:</strong> {photo.diary_entries?.users?.email}</p>
                    <p><strong>Uploaded:</strong> {new Date(photo.created_at).toLocaleDateString()}</p>
                    
                    {photo.rekognition_labels && (
                      <div>
                        <strong>AI Labels:</strong>
                        <ul className="list-disc list-inside text-xs text-gray-600">
                          {photo.rekognition_labels.labels?.map((label: string, i: number) => (
                            <li key={i}>{label}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 flex gap-2">
                    <form action="/api/admin/approve-photo" method="POST" className="inline">
                      <input type="hidden" name="photoId" value={photo.id} />
                      <button 
                        type="submit"
                        className="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700"
                      >
                        Approve
                      </button>
                    </form>
                    
                    <form action="/api/admin/remove-photo" method="POST" className="inline">
                      <input type="hidden" name="photoId" value={photo.id} />
                      <button 
                        type="submit"
                        className="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Remove
                      </button>
                    </form>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
