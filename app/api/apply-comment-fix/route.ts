import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { readFileSync } from 'fs'
import { join } from 'path'

export async function POST() {
  try {
    // Create a service role client for executing raw SQL
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: { autoRefreshToken: false, persistSession: false }
      }
    )

    // Read the migration SQL
    const migrationPath = join(process.cwd(), 'supabase', 'migrations', '014_fix_comment_policies.sql')
    const sql = readFileSync(migrationPath, 'utf8')

    console.log('Applying comment policy fix...')
    
    // Execute the SQL directly using the service role
    const { data, error } = await supabase.rpc('exec_sql', { sql })
    
    if (error) {
      console.error('SQL execution error:', error)
      
      // Try alternative approach: execute statements individually
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

      console.log('Trying alternative approach with individual statements...')
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log('Executing:', statement.substring(0, 100) + '...')
          
          const { error: stmtError } = await supabase.rpc('exec_sql', { 
            sql: statement + ';'
          })
          
          if (stmtError) {
            console.error('Statement execution error:', stmtError)
            // Don't fail on policy drop errors - they're expected if policy doesn't exist
            if (!stmtError.message?.includes('policy') || !stmtError.message?.includes('does not exist')) {
              // Try using SQL injection approach with plain query
              try {
                console.log('Attempting direct query execution...')
                const { error: directError } = await supabase
                  .from('comments')
                  .select('id')
                  .limit(1)
                  .throwOnError()
                
                // If we can query comments table, the database is accessible
                // Try the migration with a different approach
                console.log('Database is accessible, but exec_sql function may not exist')
                return NextResponse.json({ 
                  error: `Migration function not available. Please apply migration manually using Supabase SQL editor.`,
                  migration_content: sql,
                  instructions: 'Copy the migration SQL above and execute it in your Supabase project\'s SQL editor.'
                }, { status: 500 })
              } catch (testError) {
                return NextResponse.json({ 
                  error: `Database connection failed: ${stmtError.message}`,
                  statement: statement.substring(0, 200)
                }, { status: 500 })
              }
            }
          }
        }
      }
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Comment policy fixed successfully. Users can now comment on accessible entries.' 
    })
  } catch (error: any) {
    console.error('Error applying comment fix:', error)
    return NextResponse.json({ 
      error: error.message,
      migration_content: error.message.includes('not found') ? 'Migration file not found' : undefined
    }, { status: 500 })
  }
}