import { NextRequest, NextResponse } from 'next/server'
import <PERSON><PERSON> from 'stripe'
import { createSupabaseClient } from '@/lib/supabase/client'

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16'
})

export async function POST(request: NextRequest) {
  try {
    const { paymentIntentId } = await request.json()

    // Retrieve the payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId)

    if (paymentIntent.status !== 'succeeded') {
      return NextResponse.json({ error: 'Payment not completed' }, { status: 400 })
    }

    const supabase = createSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()
    
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Extract metadata
    const projectId = paymentIntent.metadata.project_id
    const waitlistSize = parseInt(paymentIntent.metadata.waitlist_size)
    const projectTitle = paymentIntent.metadata.project_title

    // Create notification campaign record
    const { data: campaign, error: campaignError } = await supabase
      .from('waitlist_notification_campaigns')
      .insert({
        project_id: projectId,
        user_id: user.id,
        campaign_name: `Launch notification for "${projectTitle}"`,
        recipient_count: waitlistSize,
        cost_cents: paymentIntent.amount,
        stripe_payment_intent_id: paymentIntentId,
        payment_status: 'paid',
        notification_status: 'ready_to_send'
      })
      .select()
      .single()

    if (campaignError) {
      console.error('Error creating campaign:', campaignError)
      return NextResponse.json({ error: 'Failed to create campaign' }, { status: 500 })
    }

    // TODO: Trigger actual notification sending
    // This would integrate with your push notification service
    await sendWaitlistNotifications(projectId, campaign.id)

    return NextResponse.json({
      success: true,
      campaignId: campaign.id,
      message: `Notifications sent to ${waitlistSize} people!`
    })

  } catch (error) {
    console.error('Error confirming payment:', error)
    return NextResponse.json(
      { error: 'Failed to process payment confirmation' },
      { status: 500 }
    )
  }
}

// Function to send notifications (placeholder)
async function sendWaitlistNotifications(projectId: string, campaignId: string) {
  // This would integrate with your push notification service
  // For now, just mark the campaign as sent
  const supabase = createSupabaseClient()
  
  await supabase
    .from('waitlist_notification_campaigns')
    .update({
      notification_status: 'sent',
      sent_at: new Date().toISOString(),
      notifications_sent: 0 // Would be actual count
    })
    .eq('id', campaignId)
}
