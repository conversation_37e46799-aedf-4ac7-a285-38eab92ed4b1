import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"
import { moderateImage } from "@/lib/aws/rekognition"

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()

    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Parse request body safely
    let photoId: string
    try {
      const body = await request.json()
      photoId = body.photoId
    } catch (parseError) {
      return NextResponse.json(
        { error: "Invalid request body" },
        { status: 400 }
      )
    }

    if (!photoId) {
      return NextResponse.json(
        { error: "Photo ID is required" },
        { status: 400 }
      )
    }

    // Get photo from database
    const { data: photo, error: photoError } = await supabase
      .from("photos")
      .select("*")
      .eq("id", photoId)
      .single()

    if (photoError || !photo) {
      return NextResponse.json(
        { error: "Photo not found" },
        { status: 404 }
      )
    }

    // Check if user owns this photo (via diary entry)
    const { data: entry, error: entryError } = await supabase
      .from("diary_entries")
      .select("user_id")
      .eq("id", photo.diary_entry_id)
      .single()

    if (entryError || !entry || entry.user_id !== user.id) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      )
    }

    // AWS moderation - photos stay visible, just get flagged if inappropriate
    let moderationResult
    let newStatus = 'approved' // Photos always stay approved/visible

    try {
      moderationResult = await moderateImage(photo.url)

      if (!moderationResult.isAppropriate) {
        // Flag for admin review but keep photo visible
        newStatus = 'flagged'
        console.log('🚩 Photo flagged for admin review:', {
          photoId,
          confidence: moderationResult.confidence,
          inappropriateLabels: moderationResult.details
        })
      } else {
        console.log('✅ Photo passed moderation:', {
          photoId,
          confidence: moderationResult.confidence
        })
      }

    } catch (moderationError) {
      console.error('AWS moderation failed, keeping photo approved:', moderationError)
      newStatus = 'approved'
      moderationResult = {
        isAppropriate: true,
        labels: [],
        confidence: 0,
        details: [{ error: 'AWS failed, auto-approved' }]
      }
    }

    const { error: updateError } = await supabase
      .from("photos")
      .update({
        moderation_status: newStatus,
        rekognition_labels: {
          labels: moderationResult.labels,
          confidence: moderationResult.confidence,
          details: moderationResult.details,
          processed_at: new Date().toISOString()
        }
      })
      .eq("id", photoId)

    if (updateError) {
      console.error('Failed to update photo status:', updateError)
      return NextResponse.json(
        { error: "Failed to update photo status" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      status: newStatus,
      isAppropriate: moderationResult.isAppropriate,
      confidence: moderationResult.confidence,
      labels: moderationResult.labels
    })

  } catch (error) {
    console.error("Error moderating photo:", error)
    return NextResponse.json(
      { error: "Failed to moderate photo" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Photo Moderation API',
    version: '1.0.0',
    endpoints: {
      POST: 'Moderate photo using AWS Rekognition',
    }
  })
}
