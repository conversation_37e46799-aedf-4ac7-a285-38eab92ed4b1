import { NextRequest, NextResponse } from 'next/server'
import webpush from 'web-push'
import { createSupabaseClient } from '@/lib/supabase/client'

// Configure web-push with VAPID keys
if (process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY && process.env.VAPID_PRIVATE_KEY && process.env.NEXT_PUBLIC_VAPID_EMAIL) {
  webpush.setVapidDetails(
    process.env.NEXT_PUBLIC_VAPID_EMAIL,
    process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY,
    process.env.VAPID_PRIVATE_KEY
  )
}

export async function POST(request: NextRequest) {
  try {
    // Check if VAPID keys are configured
    if (!process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || !process.env.VAPID_PRIVATE_KEY) {
      console.log('VAPID keys not configured, skipping push notifications')
      return NextResponse.json({ success: true, message: 'Push notifications not configured' })
    }

    const supabase = createSupabaseClient()
    
    // Get recent notifications that haven't been sent as push notifications
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*')
      .gte('sent_at', fiveMinutesAgo.toISOString())
      .is('clicked_at', null) // Use clicked_at as "processed" flag
      .limit(50) // Process in batches

    if (error) {
      console.error('Error fetching notifications:', error)
      return NextResponse.json({ error: 'Failed to fetch notifications' }, { status: 500 })
    }

    if (!notifications || notifications.length === 0) {
      return NextResponse.json({ success: true, processed: 0 })
    }

    let totalSent = 0
    let totalFailed = 0

    for (const notification of notifications) {
      try {
        // Get push subscriptions for this user
        const { data: subscriptions, error: subError } = await supabase
          .from('push_subscriptions')
          .select('id, subscription')
          .eq('user_id', notification.user_id)

        if (subError || !subscriptions || subscriptions.length === 0) {
          console.log(`No push subscriptions for user ${notification.user_id}`)
          continue
        }

        // Prepare push notification payload
        const payload = {
          title: notification.title,
          body: notification.body,
          data: {
            ...notification.data,
            notificationId: notification.id,
            url: notification.data?.url || '/'
          },
          tag: `${notification.type}-${notification.id}`,
          requireInteraction: true,
          actions: [
            {
              action: 'view',
              title: 'View'
            },
            {
              action: 'dismiss',
              title: 'Dismiss'
            }
          ]
        }

        // Send to each subscription
        for (const sub of subscriptions) {
          try {
            await webpush.sendNotification(
              sub.subscription,
              JSON.stringify(payload)
            )
            totalSent++
            console.log(`✅ Push notification sent for notification ${notification.id}`)
          } catch (pushError: any) {
            totalFailed++
            console.error(`❌ Failed to send push notification:`, pushError)
            
            // Remove invalid subscriptions
            if (pushError.statusCode === 410 || pushError.statusCode === 404) {
              await supabase
                .from('push_subscriptions')
                .delete()
                .eq('id', sub.id)
              console.log(`🗑️ Removed invalid subscription ${sub.id}`)
            }
          }
        }

        // Mark notification as processed
        await supabase
          .from('notifications')
          .update({ clicked_at: new Date().toISOString() })
          .eq('id', notification.id)

      } catch (error) {
        console.error(`Error processing notification ${notification.id}:`, error)
        totalFailed++
      }
    }

    return NextResponse.json({
      success: true,
      processed: notifications.length,
      sent: totalSent,
      failed: totalFailed
    })

  } catch (error) {
    console.error('Error in push notification processing:', error)
    return NextResponse.json(
      { error: 'Failed to process push notifications' },
      { status: 500 }
    )
  }
}
