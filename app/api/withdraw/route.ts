import { NextRequest, NextResponse } from "next/server"
import { createSupabaseServerClient } from "@/lib/supabase/client"

export async function POST(request: NextRequest) {
  try {
    const supabase = await createSupabaseServerClient()
    
    // Check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    
    if (authError || !user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    // Get user profile
    const { data: profile, error: profileError } = await supabase
      .from("users")
      .select("*")
      .eq("id", user.id)
      .single()

    if (profileError || !profile || profile.role !== 'writer') {
      return NextResponse.json(
        { error: "Only writers can request withdrawals" },
        { status: 403 }
      )
    }

    const { amount_cents } = await request.json()

    if (!amount_cents || amount_cents < 1000) { // Minimum $10
      return NextResponse.json(
        { error: "Minimum withdrawal amount is $10.00" },
        { status: 400 }
      )
    }

    // Calculate available balance
    const { data: payments } = await supabase
      .from("payments")
      .select("amount_cents, kind")
      .eq("writer_id", user.id)

    const { data: withdrawals } = await supabase
      .from("withdrawals")
      .select("amount_cents")
      .eq("writer_id", user.id)
      .neq("status", "rejected")

    const totalEarnings = payments?.reduce((sum, p) => sum + p.amount_cents, 0) || 0
    const totalWithdrawals = withdrawals?.reduce((sum, w) => sum + w.amount_cents, 0) || 0

    // Calculate platform fees based on payment type (5% for donations, 20% for subscriptions)
    const totalPlatformFees = payments?.reduce((sum, p: any) => {
      const feeRate = p.kind === 'donation' ? 0.05 : 0.20
      return sum + Math.floor(p.amount_cents * feeRate)
    }, 0) || 0

    const availableBalance = totalEarnings - totalPlatformFees - totalWithdrawals

    if (amount_cents > availableBalance) {
      return NextResponse.json(
        { error: "Insufficient balance for withdrawal" },
        { status: 400 }
      )
    }

    // Create withdrawal request
    const { data: withdrawal, error: withdrawalError } = await supabase
      .from("withdrawals")
      .insert({
        writer_id: user.id,
        amount_cents,
        status: "pending",
        requested_at: new Date().toISOString()
      })
      .select()
      .single()

    if (withdrawalError) {
      return NextResponse.json(
        { error: "Failed to create withdrawal request" },
        { status: 500 }
      )
    }

    return NextResponse.json({ 
      success: true, 
      withdrawal,
      message: "Withdrawal request submitted successfully. Processing typically takes 3-5 business days."
    })

  } catch (error) {
    console.error("Error processing withdrawal:", error)
    return NextResponse.json(
      { error: "Failed to process withdrawal request" },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'OnlyDiary Withdrawal API',
    version: '1.0.0',
    endpoints: {
      POST: 'Request withdrawal',
    }
  })
}
