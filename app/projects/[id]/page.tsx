'use client'

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"

interface Project {
  id: string
  user_id: string
  title: string
  description: string | null
  cover_image_url: string | null
  genre: string | null
  is_private: boolean
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number | null
  total_chapters: number
  total_words: number
  created_at: string
  updated_at: string
}

interface Chapter {
  id: string
  project_id: string
  user_id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
  is_published: boolean
  love_count: number
  created_at: string
  updated_at: string
}

interface Writer {
  id: string
  name: string
  bio: string | null
  avatar: string | null
  role: string
}

export default function ProjectPage({ params }: { params: Promise<{ id: string }> }) {
  const [project, setProject] = useState<Project | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [writer, setWriter] = useState<Writer | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [showWaitlistModal, setShowWaitlistModal] = useState(false)
  const [isOnWaitlist, setIsOnWaitlist] = useState(false)
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const loadProject = async () => {
      try {
        const resolvedParams = await params
        const projectId = resolvedParams.id

        // Load project details (including private ones for waitlist)
        console.log('Looking for project with ID:', projectId)

        const { data: projectData, error: projectError } = await supabase
          .from("projects")
          .select("*")
          .eq("id", projectId)
          .single()

        console.log('Project query result:', { projectData, projectError })

        if (projectError) {
          console.error('Project query error:', projectError)

          // Check for common issues
          if (projectError.message?.includes('relation "projects" does not exist')) {
            setError("Database not set up. Please run the projects migration.")
            setLoading(false)
            return
          }

          if (projectError.code === 'PGRST116') {
            setError(`Project not found. ID: ${projectId}`)
            setLoading(false)
            return
          }

          setError(`Database error: ${projectError.message} (Code: ${projectError.code})`)
          setLoading(false)
          return
        }

        if (!projectData) {
          setError("Project does not exist")
          setLoading(false)
          return
        }

        setProject(projectData)

        // Load writer info with better error handling
        console.log('Looking for writer with ID:', projectData.user_id)

        const { data: writerData, error: writerError } = await supabase
          .from("users")
          .select("id, name, bio, avatar, role")
          .eq("id", projectData.user_id)
          .single()

        console.log('Writer query result:', { writerData, writerError })

        if (writerError) {
          console.error('Writer query error:', writerError)
          setError(`Writer information not found: ${writerError.message}`)
          setLoading(false)
          return
        }

        if (!writerData) {
          setError("Writer profile does not exist")
          setLoading(false)
          return
        }

        // Check if user is actually a writer
        if (writerData.role !== 'writer') {
          setError("This project belongs to a user who is not a writer")
          setLoading(false)
          return
        }

        setWriter(writerData)

        // Load published chapters (only for public projects)
        if (!projectData.is_private) {
          const { data: chaptersData, error: chaptersError } = await supabase
            .from("chapters")
            .select("*")
            .eq("project_id", projectId)
            .eq("is_published", true)
            .order("chapter_number", { ascending: true })

          if (chaptersError) {
            console.error("Error loading chapters:", chaptersError)
          } else {
            setChapters(chaptersData || [])
          }
        }

        // Check if user is on waitlist (for private projects)
        if (projectData.is_private) {
          const { data: { user } } = await supabase.auth.getUser()
          if (user) {
            const { data: waitlistEntry } = await supabase
              .from("project_waitlist")
              .select("id")
              .eq("project_id", projectId)
              .eq("user_email", user.email)
              .single()

            setIsOnWaitlist(!!waitlistEntry)
          }
        }

      } catch (error) {
        console.error("Error loading project:", error)
        setError("Failed to load project")
      } finally {
        setLoading(false)
      }
    }

    loadProject()
  }, [params, supabase])

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading project...</div>
      </div>
    )
  }

  if (error || !project || !writer) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-serif text-gray-800 mb-4">Project Not Found</h1>
          <p className="text-gray-600 mb-6">{error || "This project doesn't exist or is private."}</p>
          <Link href="/" className="text-blue-600 hover:text-blue-700 font-medium">
            ← Back to Home
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 py-8">
        
        {/* Header */}
        <div className="mb-8">
          <Link href={`/${writer.name}`} className="text-blue-600 hover:text-blue-700 font-medium mb-4 inline-block">
            ← Back to {writer.name}'s Profile
          </Link>


        </div>

        <div className="grid lg:grid-cols-3 gap-8">
          
          {/* Project Info Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 sticky top-8">
              
              {/* Book Cover */}
              <div className="aspect-[10/16] bg-gradient-to-br from-purple-100 to-blue-100 rounded-xl overflow-hidden mb-4">
                {project.cover_image_url ? (
                  <img
                    src={project.cover_image_url}
                    alt={project.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <span className="text-6xl opacity-50">📖</span>
                  </div>
                )}
              </div>

              <h1 className="text-2xl font-serif text-gray-800 mb-2">
                {project.title}
              </h1>
              
              {project.description && (
                <p className="text-gray-600 font-serif mb-4 leading-relaxed">
                  {project.description}
                </p>
              )}

              {/* Project Stats */}
              <div className="space-y-3 text-sm mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-500">Chapters:</span>
                  <span className="font-medium">{chapters.length} published</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Words:</span>
                  <span className="font-medium">{project.total_words.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  <span className={`font-medium ${project.is_complete ? 'text-green-600' : 'text-blue-600'}`}>
                    {project.is_complete ? 'Complete' : 'In Progress'}
                  </span>
                </div>
                {project.genre && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Genre:</span>
                    <span className="font-medium">{project.genre}</span>
                  </div>
                )}
              </div>

              {/* Pricing */}
              {project.price_amount ? (
                <div className="bg-purple-50 rounded-lg p-4 mb-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600 mb-1">
                      ${(project.price_amount / 100).toFixed(2)}
                    </div>
                    <div className="text-sm text-purple-700">
                      {project.price_type === 'chapters' ? 'per 30 chapters' : 'full access'}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="bg-green-50 rounded-lg p-4 mb-4">
                  <div className="text-center">
                    <div className="text-lg font-bold text-green-600 mb-1">
                      Free to Read
                    </div>
                    <div className="text-sm text-green-700">
                      No payment required
                    </div>
                  </div>
                </div>
              )}

              {/* Author Info */}
              <div className="border-t border-gray-200 pt-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {writer.avatar ? (
                      <img
                        src={writer.avatar}
                        alt={writer.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      writer.name.charAt(0).toUpperCase()
                    )}
                  </div>
                  <div>
                    <div className="font-medium text-gray-800">{writer.name}</div>
                    <div className="text-sm text-gray-500">Author</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="lg:col-span-2">
            {project.is_private ? (
              /* Private Project - Waitlist */
              <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
                <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl">🔒</span>
                </div>
                <h2 className="text-2xl font-serif text-gray-800 mb-4">Project Coming Soon</h2>
                <p className="text-gray-600 font-serif mb-6 max-w-md mx-auto">
                  {writer.name} is still working on this project. Join the waitlist to be notified the moment it becomes available!
                </p>

                {isOnWaitlist ? (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4 max-w-md mx-auto">
                    <div className="flex items-center justify-center gap-2 text-green-700">
                      <span>✅</span>
                      <span className="font-medium">You're on the waitlist!</span>
                    </div>
                    <p className="text-green-600 text-sm mt-1">
                      We'll notify you when this project becomes available.
                    </p>
                  </div>
                ) : (
                  <Button
                    onClick={() => setShowWaitlistModal(true)}
                    className="bg-purple-600 text-white hover:bg-purple-700 px-8 py-3 text-lg"
                  >
                    🔔 Join Waitlist
                  </Button>
                )}
              </div>
            ) : (
              /* Public Project - Chapters */
              <>
                <h2 className="text-2xl font-serif text-gray-800 mb-6">Chapters</h2>

                {chapters.length === 0 ? (
                  <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <span className="text-3xl">📝</span>
                    </div>
                    <h3 className="text-xl font-serif text-gray-800 mb-3">No Chapters Published Yet</h3>
                    <p className="text-gray-600 font-serif">
                      {writer.name} is still working on this project. Check back soon for new chapters!
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {chapters.map((chapter) => (
                      <Link
                        key={chapter.id}
                        href={`/projects/${project.id}/chapters/${chapter.id}`}
                        className="block"
                      >
                        <div className="bg-white rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 hover:scale-[1.01]">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h3 className="text-lg font-serif text-gray-800 mb-2 hover:text-blue-600 transition-colors">
                                {chapter.title}
                              </h3>
                              <div className="flex items-center gap-4 text-sm text-gray-500">
                                <span>Chapter {chapter.chapter_number}</span>
                                <span>•</span>
                                <span>{chapter.word_count.toLocaleString()} words</span>
                                <span>•</span>
                                <span>{new Date(chapter.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                            <div className="text-blue-600 hover:text-blue-700">
                              <span className="text-sm font-medium">Read →</span>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Waitlist Modal */}
        {showWaitlistModal && (
          <WaitlistModal
            project={project}
            writer={writer}
            onClose={() => setShowWaitlistModal(false)}
            onSuccess={() => {
              setIsOnWaitlist(true)
              setShowWaitlistModal(false)
            }}
          />
        )}
      </div>
    </div>
  )
}

// Waitlist Modal Component
function WaitlistModal({
  project,
  writer,
  onClose,
  onSuccess
}: {
  project: Project
  writer: Writer
  onClose: () => void
  onSuccess: () => void
}) {
  const [email, setEmail] = useState("")
  const [name, setName] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const supabase = createSupabaseClient()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      setError("Email is required")
      return
    }

    setLoading(true)
    setError("")

    try {
      // Get current user if logged in
      const { data: { user } } = await supabase.auth.getUser()

      // Add to waitlist
      const { error: waitlistError } = await supabase
        .from("project_waitlist")
        .insert({
          project_id: project.id,
          user_email: email.trim(),
          user_name: name.trim() || null,
          user_id: user?.id || null
        })

      if (waitlistError) {
        if (waitlistError.code === '23505') { // Unique constraint violation
          setError("You're already on the waitlist for this project!")
          return
        }
        throw waitlistError
      }

      onSuccess()
    } catch (error) {
      console.error("Error joining waitlist:", error)
      setError("Failed to join waitlist. Please try again.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-6 w-full max-w-md">
        <h2 className="text-xl font-serif text-gray-800 mb-4">Join Waitlist</h2>

        <div className="mb-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-12 h-15 bg-gradient-to-br from-purple-100 to-blue-100 rounded-lg overflow-hidden">
              {project.cover_image_url ? (
                <img
                  src={project.cover_image_url}
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-2xl opacity-50">📖</span>
                </div>
              )}
            </div>
            <div>
              <h3 className="font-serif text-lg text-gray-800">{project.title}</h3>
              <p className="text-sm text-gray-600">by {writer.name}</p>
            </div>
          </div>

          <p className="text-gray-600 text-sm">
            Be the first to know when this project becomes available. We'll send you an email notification the moment it's published!
          </p>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Email Address *
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Name (Optional)
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
              placeholder="Your name"
            />
          </div>

          <div className="flex gap-3 mt-6">
            <Button
              type="button"
              onClick={onClose}
              variant="secondary"
              className="flex-1"
              disabled={loading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={loading}
              className="flex-1 bg-purple-600 text-white hover:bg-purple-700"
            >
              {loading ? 'Joining...' : 'Join Waitlist'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
