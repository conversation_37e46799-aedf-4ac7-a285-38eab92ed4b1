import { createServerSupabaseClient } from '@/lib/supabase/server';
import { notFound } from 'next/navigation';
import SubscriberProfileClient from '@/components/SubscriberProfileClient';
import { Database } from '@/lib/supabase/database.types';

type Props = {
  params: { id: string };
};

export default async function SubscriberProfilePage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  const supabase = await createServerSupabaseClient();

  // Fetch subscriber profile
  const { data: profile, error: profileError } = await supabase
    .from('users')
    .select(`
      id, 
      name, 
      avatar, 
      bio, 
      profile_picture_url,
      social_twitter,
      social_instagram,
      social_website,
      flower_count,
      created_at
    `)
    .eq('id', id)
    .single();

  if (profileError || !profile) {
    notFound();
  }

  // Fetch flowers received by the subscriber
  const { data: flowers, error: flowersError } = await supabase
    .from('flowers')
    .select(`
      id, 
      message, 
      created_at, 
      giver_id,
      giver:giver_id (
        name,
        avatar
      )
    `)
    .eq('receiver_id', id)
    .order('created_at', { ascending: false });

  if (flowersError) {
    console.error('Error fetching flowers:', flowersError);
  }

  // Fetch favorite creators - use manual join
  let favoriteCreators: any[] = [];
  try {
    const { data: favorites } = await supabase
      .from('favorite_creators')
      .select('id, created_at, writer_id')
      .eq('user_id', id)
      .order('created_at', { ascending: false });

    if (favorites && favorites.length > 0) {
      // Get writer details separately
      const writerIds = favorites.map(f => f.writer_id);
      const { data: writers } = await supabase
        .from('users')
        .select('id, name, avatar, profile_picture_url, bio')
        .in('id', writerIds);

      // Combine the data
      favoriteCreators = favorites.map(fav => ({
        id: fav.id,
        created_at: fav.created_at,
        writer: writers?.find(w => w.id === fav.writer_id) || null
      }));
    }
  } catch (err) {
    console.log('Error fetching favorites:', err);
    favoriteCreators = [];
  }

  const typedFlowers = flowers as unknown as Array<{
    id: string;
    message: string;
    created_at: string;
    giver_id: string;
    giver: {
      name: string;
      avatar: string;
    } | null;
  }>;

  return (
    <SubscriberProfileClient
      profile={profile}
      flowers={typedFlowers || []}
      favoriteCreators={favoriteCreators}
    />
  );
}
