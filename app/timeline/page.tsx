'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Heart, MessageCircle, Clock, Bookmark } from 'lucide-react'

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  created_at: string
  is_free: boolean
  bundle_count: number
  user: {
    id: string
    name: string
    avatar?: string
    profile_picture_url?: string
  }
  loves_count: number
  comments_count: number
  has_access: boolean
  credits_required: number
  is_recent?: boolean
  days_old?: number
  photos?: any[]
}

interface UserCredits {
  [userId: string]: {
    credits_remaining: number
    last_reset: string
  }
}

export default function TimelinePage() {
  console.log('TimelinePage: Component rendering start.')
  const [entries, setEntries] = useState<DiaryEntry[]>([])
  const [loading, setLoading] = useState(true)
  const [readingEntryId, setReadingEntryId] = useState<string | null>(null)
  const [user, setUser] = useState<any>(null)
  const [hasError, setHasError] = useState(false) // New state for error
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    console.log('Timeline: useEffect triggered, checking auth...')
    checkAuth()
    // Add a fallback to hide spinner after a delay, in case of unexpected hangs
    const timeoutId = setTimeout(() => {
      if (loading) { // Only set to false if still loading
        console.log('Timeline: Forcing loading to false after timeout.')
        setLoading(false)
      }
    }, 10000); // 10 seconds

    return () => clearTimeout(timeoutId); // Clear timeout on unmount
  }, [])

  const checkAuth = async () => {
    try {
      console.log('Timeline: Attempting to get user session...')
      const { data: { user: authUser }, error: authUserError } = await supabase.auth.getUser()
      if (authUserError) {
        console.error('Timeline: Error getting auth user:', authUserError)
        setHasError(true) // Set error state
        setLoading(false) // Hide spinner on auth error
        router.push('/login')
        return
      }
      if (!authUser) {
        console.log('Timeline: No authenticated user found, redirecting to login.')
        setLoading(false) // Hide spinner if no user
        router.push('/login')
        return
      }
      console.log('Timeline: User authenticated:', authUser.id)
      setUser(authUser)
      loadEntries(authUser)
    } catch (error) {
      console.error('Timeline: Auth error in checkAuth:', error)
      setHasError(true) // Set error state
      setLoading(false) // Hide spinner on unexpected auth error
      router.push('/login')
    }
  }

  const loadEntries = async (currentUser?: any) => {
    try {
      const userToUse = currentUser || user
      if (!userToUse) {
        console.log('Timeline: No user found for loading entries. Exiting loadEntries.')
        setLoading(false)
        return
      }

      console.log('Timeline: Starting to load entries for user:', userToUse.id)

      // Get user's follows (this is how users follow creators)
      console.log('Timeline: Fetching follows...')
      const { data: follows, error: followsError } = await supabase
        .from('follows')
        .select('*')
        .eq('follower_id', userToUse.id)

      console.log('Timeline: Follows query result:', { follows, followsError })

      if (followsError) {
        console.error('Timeline: Error fetching follows:', {
          message: followsError.message,
          details: followsError.details,
          hint: followsError.hint,
          code: followsError.code
        })
        setEntries([])
        setHasError(true) // Set error state
        setLoading(false)
        return
      }

      if (!follows || follows.length === 0) {
        console.log('Timeline: No follows found for user. Setting empty entries.')
        // No follows = empty timeline
        setEntries([])
        setLoading(false)
        return
      }

      const creatorIds = follows.map(f => f.writer_id)
      console.log('Timeline: Looking for entries from creators:', creatorIds)

      // Get entries from bookmarked creators, sorted by likes then date
      console.log('Timeline: Fetching diary entries...')
      const { data, error: entriesError } = await supabase
        .from('diary_entries')
        .select(`
          id,
          title,
          body_md,
          created_at,
          is_free,
          love_count,
          user_id,
          user:users!user_id (
            id,
            name,
            avatar,
            profile_picture_url
          )
        `)
        .in('user_id', creatorIds)
        .eq('is_hidden', false)
        .order('love_count', { ascending: false })
        .order('created_at', { ascending: false })
        .limit(50)

      console.log('Timeline: Entries query result:', { data, entriesError })

      if (entriesError) {
        console.error('Timeline: Error fetching entries:', entriesError)
        setEntries([])
        setHasError(true) // Set error state
        setLoading(false)
        return
      }

      // Get photos for all entries
      const entryIds = data?.map(entry => entry.id) || []
      console.log('Timeline: Fetching photos for entries:', entryIds)
      const { data: photos, error: photosError } = await supabase
        .from('photos')
        .select('*')
        .in('diary_entry_id', entryIds)
        .eq('moderation_status', 'approved')
        .order('created_at', { ascending: true })

      if (photosError) {
        console.error('Timeline: Error fetching photos:', photosError)
        // Continue without photos if there's an error, or handle as needed
      }

      // Group photos by entry ID
      const photosByEntry: { [key: string]: any[] } = {}
      photos?.forEach(photo => {
        if (!photosByEntry[photo.diary_entry_id]) {
          photosByEntry[photo.diary_entry_id] = []
        }
        photosByEntry[photo.diary_entry_id].push(photo)
      })

      // Add photos to entries
      const entriesWithPhotos = data?.map(entry => ({
        ...entry,
        photos: photosByEntry[entry.id] || []
      })) || []

      // Mix entries by creator (not all from one person)
      const entriesByCreator: { [key: string]: any[] } = {}
      entriesWithPhotos?.forEach(entry => {
        if (!entriesByCreator[entry.user_id]) {
          entriesByCreator[entry.user_id] = []
        }
        entriesByCreator[entry.user_id].push(entry)
      })

      // Interleave entries from different creators
      const mixedEntries: any[] = []
      const maxPerCreator = Math.ceil(50 / creatorIds.length)

      for (let i = 0; i < maxPerCreator; i++) {
        Object.values(entriesByCreator).forEach((creatorEntries: any[]) => {
          if (creatorEntries[i]) {
            mixedEntries.push(creatorEntries[i])
          }
        })
      }

      console.log('Timeline: Final mixed entries count:', mixedEntries.length)
      setEntries(mixedEntries.slice(0, 50))
      console.log('Timeline: Entries loaded successfully.')
    } catch (error) {
      console.error('Timeline: Unexpected error in loadEntries:', error)
      setHasError(true) // Set error state
    } finally {
      console.log('Timeline: Setting loading to false.')
      setLoading(false)
    }
  }



  const handleReadEntry = (entryId: string) => {
    setReadingEntryId(entryId)
    window.location.href = `/d/${entryId}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (hasError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4">
        <div className="bg-white rounded-2xl p-12 text-center shadow-sm max-w-md w-full">
          <h3 className="text-xl font-serif text-red-600 mb-2">Error Loading Timeline</h3>
          <p className="text-gray-600 mb-6">
            There was a problem loading your timeline. Please try again later or contact support.
          </p>
          <button
            onClick={() => {
              setHasError(false);
              setLoading(true);
              checkAuth(); // Re-attempt loading
            }}
            className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        <div className="mb-6 sm:mb-8">
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-2">Timeline</h1>
          <p className="text-gray-600 font-serif text-sm sm:text-base">Latest stories from creators</p>
        </div>

        {entries.length > 0 ? (
          <div className="space-y-4 sm:space-y-6">
            {entries.map((entry) => (
              <div key={entry.id} className="bg-white rounded-2xl p-4 sm:p-6 shadow-sm">
                <div className="flex items-start gap-3 sm:gap-4">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                    {entry.user?.avatar || entry.user?.profile_picture_url ? (
                      <img
                        src={entry.user.avatar || entry.user.profile_picture_url}
                        alt={entry.user.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm sm:text-lg font-serif text-gray-500">
                        {entry.user?.name?.charAt(0).toUpperCase() || '?'}
                      </span>
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-gray-900 text-sm sm:text-base truncate">{entry.user?.name || 'Unknown'}</h3>
                      <span className="text-gray-400 text-xs sm:text-sm">•</span>
                      <span className="text-gray-500 text-xs sm:text-sm">
                        {formatDate(entry.created_at)}
                      </span>
                    </div>

                    <h4 className="text-base sm:text-lg font-serif text-gray-800 mb-3 line-clamp-2">{entry.title}</h4>
                    <p className="text-gray-600 leading-relaxed mb-4 text-sm sm:text-base line-clamp-3">
                      {entry.body_md?.slice(0, 200)}...
                    </p>

                    {/* Photos - Mobile Responsive */}
                    {entry.photos && entry.photos.length > 0 && (
                      <div className="mb-4">
                        {entry.photos.length === 1 ? (
                          // Single photo - full width
                          <div className="rounded-lg overflow-hidden">
                            <img
                              src={entry.photos[0].url}
                              alt="Entry photo"
                              className="w-full h-48 sm:h-64 object-cover"
                            />
                          </div>
                        ) : entry.photos.length === 2 ? (
                          // Two photos - side by side
                          <div className="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                            {entry.photos.slice(0, 2).map((photo: any, index: number) => (
                              <img
                                key={index}
                                src={photo.url}
                                alt={`Entry photo ${index + 1}`}
                                className="w-full h-32 sm:h-40 object-cover"
                              />
                            ))}
                          </div>
                        ) : (
                          // Multiple photos - grid layout
                          <div className="grid grid-cols-2 gap-2 rounded-lg overflow-hidden">
                            {entry.photos.slice(0, 3).map((photo: any, index: number) => (
                              <div key={index} className={index === 0 ? "col-span-2" : ""}>
                                <img
                                  src={photo.url}
                                  alt={`Entry photo ${index + 1}`}
                                  className={`w-full object-cover ${
                                    index === 0 ? "h-40 sm:h-48" : "h-24 sm:h-32"
                                  }`}
                                />
                              </div>
                            ))}
                            {entry.photos.length > 3 && (
                              <div className="relative">
                                <img
                                  src={entry.photos[3].url}
                                  alt="More photos"
                                  className="w-full h-24 sm:h-32 object-cover"
                                />
                                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                                  <span className="text-white font-medium text-sm">
                                    +{entry.photos.length - 3}
                                  </span>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    )}

                    <button
                      onClick={() => handleReadEntry(entry.id)}
                      disabled={readingEntryId === entry.id}
                      className="inline-block bg-blue-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 text-sm sm:text-base"
                    >
                      {readingEntryId === entry.id ? (
                        <div className="flex items-center gap-2">
                          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                          Loading...
                        </div>
                      ) : (
                        'Read Entry'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="bg-white rounded-2xl p-12 text-center shadow-sm">
            <h3 className="text-xl font-serif text-gray-800 mb-2">No Stories Yet</h3>
            <p className="text-gray-600 mb-6">No entries found</p>
            <Link
              href="/discover"
              className="inline-block bg-blue-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-blue-700 transition-colors"
            >
              Discover Creators
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
