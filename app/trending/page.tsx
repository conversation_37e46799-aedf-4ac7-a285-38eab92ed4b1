import { createSupabaseServerClient } from "@/lib/supabase/client"
import Link from "next/link"

interface TopDiaryEntry {
  entry_id: string
  title: string
  writer_name: string
  writer_id: string
  writer_avatar: string | null
  hourly_loves: number
  total_loves: number
  first_photo_url: string | null
  created_at: string
  is_free: boolean
}

function formatTimeAgo(dateString: string) {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) return "Just now"
  if (diffInHours === 1) return "1 hour ago"
  if (diffInHours < 24) return `${diffInHours} hours ago`
  
  const diffInDays = Math.floor(diffInHours / 24)
  if (diffInDays === 1) return "1 day ago"
  return `${diffInDays} days ago`
}

export default async function TrendingPage() {
  const supabase = await createSupabaseServerClient()

  // Get recent entries with love counts
  const { data: fallbackEntries } = await supabase
    .from('diary_entries')
    .select(`
      id,
      title,
      created_at,
      is_free,
      love_count,
      users!inner (
        id,
        name,
        profile_picture_url
      ),
      photos (
        url
      )
    `)
    .eq('is_hidden', false)
    .gte('created_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
    .order('love_count', { ascending: false })
    .limit(20)

  const entries: TopDiaryEntry[] = fallbackEntries?.map((entry: any) => ({
    entry_id: entry.id,
    title: entry.title,
    writer_name: entry.users.name,
    writer_id: entry.users.id,
    writer_avatar: entry.users.profile_picture_url,
    hourly_loves: entry.love_count || 0, // Use total loves as hourly for now
    total_loves: entry.love_count || 0,
    first_photo_url: entry.photos?.[0]?.url || null,
    created_at: entry.created_at,
    is_free: entry.is_free
  })) || []

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-6">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔥 Trending Diary Entries
          </h1>
          <p className="text-gray-600">
            The most loved diary entries from the past week
          </p>
          <p className="text-sm text-gray-500 mt-1 italic">
            Rankings update automatically every hour
          </p>

          {/* Call to Action for Supporting Creators */}
          <div className="mt-6">
            <Link
              href="/discover"
              className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              <span>💝</span>
              Explore Creators to Support
            </Link>
            <p className="text-xs text-gray-500 mt-2">
              Discover amazing writers and support their work
            </p>
          </div>
        </div>

        {/* Top Entries Grid */}
        {entries.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm p-8 text-center">
            <p className="text-gray-500">No trending entries right now</p>
            <p className="text-sm text-gray-400 mt-2">
              Check back later or be the first to create a trending post!
            </p>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {entries.map((entry, index) => (
              <div key={entry.entry_id} className="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow">
                {/* Ranking Badge */}
                <div className="relative">
                  <div className={`absolute top-3 left-3 z-10 w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                    index === 0 ? 'bg-yellow-500' :
                    index === 1 ? 'bg-gray-400' :
                    index === 2 ? 'bg-orange-500' :
                    'bg-gray-600'
                  }`}>
                    #{index + 1}
                  </div>

                  {/* Photo or Placeholder */}
                  {entry.first_photo_url ? (
                    <img
                      src={entry.first_photo_url}
                      alt={entry.title}
                      className="w-full h-48 object-cover"
                    />
                  ) : (
                    <div className="w-full h-48 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                      <span className="text-4xl text-gray-400">📝</span>
                    </div>
                  )}
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-bold text-gray-900 mb-2 line-clamp-2">
                    {entry.title}
                  </h3>

                  {/* Writer Info */}
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-8 h-8 rounded-full overflow-hidden bg-gray-200 flex-shrink-0">
                      {entry.writer_avatar ? (
                        <img
                          src={entry.writer_avatar}
                          alt={entry.writer_name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-300 flex items-center justify-center text-xs text-gray-600">
                          {entry.writer_name.charAt(0).toUpperCase()}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {entry.writer_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatTimeAgo(entry.created_at)}
                      </p>
                    </div>

                    {entry.is_free && (
                      <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                        FREE
                      </span>
                    )}
                  </div>

                  {/* Stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <span>❤️ {entry.hourly_loves} this hour</span>
                    <span>{entry.total_loves} total loves</span>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Link
                      href={`/d/${entry.entry_id}`}
                      className="flex-1 bg-gray-800 text-white text-center py-2 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
                    >
                      Read Entry
                    </Link>
                    <Link
                      href={`/u/${entry.writer_id}`}
                      className="bg-gray-100 text-gray-700 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors"
                    >
                      Subscribe
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}


      </div>
    </div>
  )
}
