'use client'

import { useState, useEffect, useTransition } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { SmartTypography, useSmartFormatting, getTypographyMetrics } from "@/components/SmartTypography"
import { useDeviceCapabilities } from "@/hooks/useDeviceDetection"
import { getUserErrorMessage } from "@/lib/utils/errorHandling"

interface Project {
  id: string
  title: string
}

interface Chapter {
  id: string
  project_id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
  is_published: boolean
  love_count: number
}

export default function ChapterWritePage({ params }: { 
  params: Promise<{ id: string; chapterId: string }> 
}) {
  const [project, setProject] = useState<Project | null>(null)
  const [chapter, setChapter] = useState<Chapter | null>(null)
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [autoSaveStatus, setAutoSaveStatus] = useState("")
  const [showPreview, setShowPreview] = useState(false)
  const [zenMode, setZenMode] = useState(false)
  const [typographyMetrics, setTypographyMetrics] = useState<any>(null)
  const [isPending, startTransition] = useTransition()
  const [showPublishModal, setShowPublishModal] = useState(false)
  
  const router = useRouter()
  const supabase = createSupabaseClient()
  const deviceCapabilities = useDeviceCapabilities()

  useEffect(() => {
    const initializePage = async () => {
      const resolvedParams = await params
      const { id: projectId, chapterId } = resolvedParams

      const { data: { user: authUser }, error } = await supabase.auth.getUser()
      
      if (error || !authUser) {
        router.push('/login')
        return
      }

      // Get user profile
      const { data: profile } = await supabase
        .from("users")
        .select("*")
        .eq("id", authUser.id)
        .single()

      if (!profile || profile.role !== 'writer') {
        router.push('/')
        return
      }

      setUser(profile)

      // Load project
      const { data: projectData } = await supabase
        .from("projects")
        .select("id, title")
        .eq("id", projectId)
        .eq("user_id", authUser.id)
        .single()

      if (!projectData) {
        router.push('/write/projects')
        return
      }

      setProject(projectData)

      // Load chapter
      const { data: chapterData } = await supabase
        .from("chapters")
        .select("*")
        .eq("id", chapterId)
        .eq("user_id", authUser.id)
        .single()

      if (!chapterData) {
        router.push(`/write/projects/${projectId}`)
        return
      }

      setChapter(chapterData)
      setTitle(chapterData.title)
      setContent(chapterData.content)
      setLoading(false)
    }

    initializePage()
  }, [params, router, supabase])

  // Auto-save functionality
  useEffect(() => {
    if (!chapter || !title || !content) return

    const autoSaveTimer = setTimeout(async () => {
      if (title === chapter.title && content === chapter.content) return

      setSaving(true)
      setAutoSaveStatus("Saving...")

      try {
        const wordCount = content.trim().split(/\s+/).filter(word => word.length > 0).length

        const { error } = await supabase
          .from("chapters")
          .update({
            title: title.trim(),
            content: content,
            word_count: wordCount,
            updated_at: new Date().toISOString()
          })
          .eq("id", chapter.id)

        if (error) throw error

        setAutoSaveStatus("Saved automatically")
        setTimeout(() => setAutoSaveStatus(""), 3000)
      } catch (error) {
        console.error('Auto-save failed:', error)
        const errorMessage = getUserErrorMessage(error)
        setAutoSaveStatus(errorMessage.includes('Connection') ? "Connection issue - will retry" : "Auto-save failed")
      } finally {
        setSaving(false)
      }
    }, 2000)

    return () => clearTimeout(autoSaveTimer)
  }, [title, content, chapter, supabase])

  // Typography metrics
  useEffect(() => {
    if (content.length > 50) {
      const metrics = getTypographyMetrics(content)
      setTypographyMetrics(metrics)
    }
  }, [content])

  // Smart formatting
  useSmartFormatting(content, (formatted) => {
    if (formatted !== content) {
      setContent(formatted)
    }
  })

  const handlePublishClick = () => {
    if (chapter?.is_published) {
      // If already published, unpublish directly
      handleUnpublish()
    } else {
      // If not published, show publish options modal
      setShowPublishModal(true)
    }
  }

  const handleUnpublish = async () => {
    if (!chapter) return

    startTransition(async () => {
      try {
        const { error } = await supabase
          .from("chapters")
          .update({
            is_published: false,
            updated_at: new Date().toISOString()
          })
          .eq("id", chapter.id)

        if (error) throw error

        setChapter(prev => prev ? { ...prev, is_published: false } : null)
      } catch (error) {
        console.error('Error unpublishing chapter:', error)
      }
    })
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-gray-50 flex items-center justify-center">
        <div className="text-gray-600 font-serif">Loading...</div>
      </div>
    )
  }

  if (!project || !chapter || !user) return null

  return (
    <div className={`min-h-screen transition-all duration-1000 ${
      zenMode 
        ? 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900' 
        : 'bg-gradient-to-br from-gray-50 via-white to-gray-50'
    }`}>
      


      <div className={`max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8 transition-all duration-700 ${
        zenMode ? 'max-w-3xl' : ''
      }`}>
        
        {/* Header */}
        <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 gap-4 transition-all duration-700 ${
          zenMode ? 'opacity-30 hover:opacity-100' : 'opacity-100'
        }`}>
          <div>
            <h1 className={`font-serif transition-all duration-500 ${
              zenMode 
                ? 'text-xl text-white/90' 
                : 'text-2xl sm:text-3xl text-gray-800'
            }`}>
              {project.title}
            </h1>
            <p className={`font-serif mt-1 text-sm sm:text-base transition-all duration-500 ${
              zenMode 
                ? 'text-white/60' 
                : 'text-gray-600'
            }`}>
              Chapter {chapter.chapter_number}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Theme Toggle */}
            <button
              onClick={() => setZenMode(!zenMode)}
              className={`flex items-center gap-2 px-3 py-2 rounded-lg text-sm font-medium transition-all duration-500 ${
                zenMode
                  ? 'text-white/90 bg-slate-700/50 hover:bg-slate-600/50 border border-slate-600/30'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200 border border-gray-200'
              }`}
              title={zenMode ? 'Switch to Bright Mode' : 'Switch to Dark Mode'}
            >
              {zenMode ? (
                <>
                  <span>☀️</span>
                  <span className="hidden sm:inline">Bright</span>
                </>
              ) : (
                <>
                  <span>🌙</span>
                  <span className="hidden sm:inline">Dark</span>
                </>
              )}
            </button>

            <button
              onClick={() => setShowPreview(!showPreview)}
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-500 ${
                zenMode
                  ? 'text-white/80 bg-slate-700/50 hover:bg-slate-600/50'
                  : 'text-gray-700 bg-gray-100 hover:bg-gray-200'
              } ${showPreview ? 'ring-2 ring-blue-500/30' : ''}`}
            >
              {showPreview ? '✏️ Edit' : '👁️ Preview'}
            </button>

            <Link
              href={`/write/projects/${project.id}`}
              className={`font-medium text-sm transition-all duration-500 ${
                zenMode ? 'text-white/80 hover:text-white' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              ← Project
            </Link>
          </div>
        </div>

        {/* Writing Interface */}
        <div className={`rounded-2xl shadow-sm transition-all duration-700 ${
          zenMode 
            ? 'bg-slate-800/50 backdrop-blur-xl border border-slate-700/30' 
            : 'bg-white/90 backdrop-blur-sm border border-gray-100/50'
        }`}>
          
          {/* Chapter Header */}
          <div className={`transition-all duration-700 ${
            zenMode 
              ? 'p-6 sm:p-8 border-b border-slate-700/30' 
              : 'p-4 sm:p-6 border-b border-gray-200'
          }`}>
            <input
              type="text"
              placeholder="Chapter Title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className={`w-full font-serif border-none outline-none bg-transparent transition-all duration-500 ${
                zenMode 
                  ? 'text-xl sm:text-2xl text-white/90 placeholder-white/30' 
                  : 'text-xl sm:text-2xl text-gray-900 placeholder-gray-400'
              }`}
              maxLength={200}
            />
          </div>
          
          {/* Writing Area */}
          <div className={`transition-all duration-700 ${
            zenMode ? 'p-8 sm:p-12' : 'p-4 sm:p-6'
          }`}>
            
            {showPreview ? (
              <div className={`transition-all duration-700 ${
                zenMode ? 'min-h-[70vh]' : 'min-h-[24rem] sm:min-h-[32rem]'
              }`}>
                <SmartTypography 
                  content={content}
                  zenMode={zenMode}
                  isPreview={true}
                  isDesktop={deviceCapabilities.isDesktop}
                  enableAdvancedFeatures={deviceCapabilities.shouldUseAdvancedAnimations}
                  className="w-full"
                />
                {!content && (
                  <div className={`text-center py-20 ${
                    zenMode ? 'text-white/40' : 'text-gray-400'
                  }`}>
                    <p className="text-lg font-serif">Your beautifully formatted chapter will appear here...</p>
                  </div>
                )}
              </div>
            ) : (
              <textarea
                placeholder={zenMode ? "Let your story unfold..." : "Start writing your chapter..."}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className={`w-full font-serif leading-relaxed border-none outline-none bg-transparent resize-none transition-all duration-700 ${
                  zenMode
                    ? 'h-[70vh] text-lg sm:text-xl text-white/90 placeholder-white/30'
                    : 'h-64 sm:h-96 text-base sm:text-lg text-gray-900 placeholder-gray-400'
                }`}
                style={{
                  lineHeight: '1.75',
                }}
              />
            )}
            
            {/* Stats Bar */}
            <div className={`flex flex-col sm:flex-row sm:items-center sm:justify-between mt-4 gap-2 transition-all duration-700 ${
              zenMode ? 'opacity-30 hover:opacity-100' : 'opacity-100'
            }`}>
              
              <div className={`text-sm transition-all duration-500 ${
                zenMode ? 'text-white/50' : 'text-gray-500'
              }`}>
                {typographyMetrics && (
                  <div className="flex items-center gap-4">
                    <span>{typographyMetrics.words} words</span>
                    <span>•</span>
                    <span>{typographyMetrics.paragraphs} paragraphs</span>
                    <span>•</span>
                    <span>{typographyMetrics.readingTime} min read</span>
                  </div>
                )}
              </div>
              
              {autoSaveStatus && (
                <div className={`text-sm font-medium px-3 py-1 rounded-full transition-all duration-500 ${
                  zenMode 
                    ? autoSaveStatus.includes('failed')
                      ? 'text-red-400 bg-red-900/20'
                      : autoSaveStatus.includes('Saving')
                      ? 'text-blue-400 bg-blue-900/20'
                      : 'text-green-400 bg-green-900/20'
                    : autoSaveStatus.includes('failed')
                    ? 'text-red-600 bg-red-50'
                    : autoSaveStatus.includes('Saving')
                    ? 'text-blue-600 bg-blue-50'
                    : 'text-green-600 bg-green-50'
                }`}>
                  {autoSaveStatus.includes('Saving') && '💾 '}
                  {autoSaveStatus.includes('Saved') && '✅ '}
                  {autoSaveStatus.includes('failed') && '❌ '}
                  {autoSaveStatus}
                </div>
              )}
            </div>
          </div>
          
          {/* Actions */}
          <div className={`transition-all duration-700 ${
            zenMode 
              ? 'p-6 sm:p-8 border-t border-slate-700/30' 
              : 'p-4 sm:p-6 border-t border-gray-200'
          }`}>
            
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handlePublishClick}
                  isLoading={isPending}
                  className={`w-full sm:w-auto py-3 transition-all duration-500 ${
                    chapter.is_published
                      ? zenMode
                        ? 'bg-slate-700/50 text-white/80 hover:bg-slate-600/50'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                      : zenMode
                        ? 'bg-white/90 text-slate-900 hover:bg-white'
                        : 'bg-purple-600 text-white hover:bg-purple-700'
                  }`}
                >
                  {chapter.is_published ? '📝 Unpublish' : '✨ Publish Chapter'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Publish Options Modal */}
        {showPublishModal && (
          <PublishModal
            chapter={chapter}
            project={project}
            onClose={() => setShowPublishModal(false)}
            onSuccess={(updatedChapter) => {
              setChapter(updatedChapter)
              setShowPublishModal(false)
            }}
          />
        )}
      </div>
    </div>
  )
}

// Publish Modal Component
function PublishModal({
  chapter,
  project,
  onClose,
  onSuccess
}: {
  chapter: Chapter
  project: Project
  onClose: () => void
  onSuccess: (chapter: Chapter) => void
}) {
  const [publishType, setPublishType] = useState<'free' | 'paid' | 'project'>('project')
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  const handlePublish = async () => {
    setLoading(true)
    try {
      const { data: updatedChapter, error } = await supabase
        .from("chapters")
        .update({
          is_published: true,
          // You can add additional fields here for publish type if needed
          updated_at: new Date().toISOString()
        })
        .eq("id", chapter.id)
        .select()
        .single()

      if (error) throw error

      onSuccess(updatedChapter)
    } catch (error) {
      console.error('Error publishing chapter:', error)
      const errorMessage = getUserErrorMessage(error)
      alert(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-2xl p-4 sm:p-6 w-full max-w-lg max-h-[90vh] overflow-y-auto">
        <h2 className="text-lg sm:text-xl font-serif text-gray-800 mb-4 sm:mb-6">Publish Chapter</h2>

        <div className="space-y-3 sm:space-y-4 mb-4 sm:mb-6">
          {/* Free Chapter Option */}
          <label className="flex items-start gap-3 p-3 sm:p-4 border border-gray-200 rounded-lg sm:rounded-xl hover:border-gray-300 transition-colors cursor-pointer">
            <input
              type="radio"
              value="free"
              checked={publishType === 'free'}
              onChange={(e) => setPublishType(e.target.value as 'free')}
              className="mt-1 flex-shrink-0"
            />
            <div className="min-w-0">
              <div className="font-medium text-gray-900 mb-1 text-sm sm:text-base">📖 Free Chapter</div>
              <div className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Anyone can read this chapter for free. Great for attracting new readers and building your audience.
              </div>
            </div>
          </label>

          {/* Paid Access Option */}
          <label className="flex items-start gap-3 p-3 sm:p-4 border border-gray-200 rounded-lg sm:rounded-xl hover:border-gray-300 transition-colors cursor-pointer">
            <input
              type="radio"
              value="paid"
              checked={publishType === 'paid'}
              onChange={(e) => setPublishType(e.target.value as 'paid')}
              className="mt-1 flex-shrink-0"
            />
            <div className="min-w-0">
              <div className="font-medium text-gray-900 mb-1 text-sm sm:text-base">💰 Paid Access Only</div>
              <div className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Only readers who have purchased access to your project can read this chapter. Maximizes revenue from dedicated fans.
              </div>
            </div>
          </label>

          {/* Part of Project Option */}
          <label className="flex items-start gap-3 p-3 sm:p-4 border border-gray-200 rounded-lg sm:rounded-xl hover:border-gray-300 transition-colors cursor-pointer">
            <input
              type="radio"
              value="project"
              checked={publishType === 'project'}
              onChange={(e) => setPublishType(e.target.value as 'project')}
              className="mt-1 flex-shrink-0"
            />
            <div className="min-w-0">
              <div className="font-medium text-gray-900 mb-1 text-sm sm:text-base">📚 Part of Project</div>
              <div className="text-xs sm:text-sm text-gray-600 leading-relaxed">
                Follows your project's pricing settings. If your project is free, this will be free. If paid, this requires payment according to your project pricing.
              </div>
            </div>
          </label>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            onClick={onClose}
            variant="secondary"
            className="flex-1 py-3"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            onClick={handlePublish}
            isLoading={loading}
            className="flex-1 bg-purple-600 text-white hover:bg-purple-700 py-3"
          >
            {loading ? 'Publishing...' : 'Publish Chapter'}
          </Button>
        </div>
      </div>
    </div>
  )
}
