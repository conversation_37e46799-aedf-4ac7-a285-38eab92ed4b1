'use client'

import { useState, useEffect } from 'react'
import { createSupabaseClient } from '@/lib/supabase/client'
import { Heart } from 'lucide-react'

interface Flower {
  id: string
  created_at: string
  message: string
  giver: {
    id: string
    name: string
    avatar?: string
  }
}

interface FlowersSectionProps {
  userId: string
  isOwnProfile: boolean
}

export function FlowersSection({ userId, isOwnProfile }: FlowersSectionProps) {
  const [flowers, setFlowers] = useState<Flower[]>([])
  const [loading, setLoading] = useState(true)
  const [showGiveForm, setShowGiveForm] = useState(false)
  const [message, setMessage] = useState('')
  const [submitting, setSubmitting] = useState(false)
  const supabase = createSupabaseClient()

  useEffect(() => {
    loadFlowers()
  }, [userId])

  const loadFlowers = async () => {
    try {
      const { data, error } = await supabase
        .from('flowers')
        .select(`
          id,
          created_at,
          message,
          giver:users!giver_id (
            id,
            name,
            avatar
          )
        `)
        .eq('receiver_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error loading flowers:', error)
      } else {
        setFlowers(data || [])
      }
    } catch (err) {
      console.error('Error loading flowers:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleGiveFlower = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!message.trim()) return

    setSubmitting(true)
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return

      const { error } = await supabase
        .from('flowers')
        .insert({
          receiver_id: userId,
          giver_id: user.id,
          message: message.trim()
        })

      if (error) {
        console.error('Error giving flower:', error)
        alert('Failed to give flower. Please try again.')
      } else {
        setMessage('')
        setShowGiveForm(false)
        loadFlowers() // Reload to show new flower
      }
    } catch (err) {
      console.error('Error giving flower:', err)
      alert('An unexpected error occurred')
    } finally {
      setSubmitting(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (loading) {
    return (
      <div className="bg-white rounded-2xl p-8 shadow-sm">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-32 mb-4"></div>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex gap-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1">
                  <div className="h-4 bg-gray-200 rounded w-24 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-2xl p-8 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center">
            <Heart className="w-5 h-5 text-pink-600" />
          </div>
          <h2 className="text-2xl font-serif text-gray-800">
            Flowers ({flowers.length})
          </h2>
        </div>
        
        {!isOwnProfile && (
          <button
            onClick={() => setShowGiveForm(!showGiveForm)}
            className="bg-pink-600 text-white px-4 py-2 rounded-xl font-medium hover:bg-pink-700 transition-colors"
          >
            Give Flowers 🌹
          </button>
        )}
      </div>

      {/* Give Flower Form */}
      {showGiveForm && (
        <div className="mb-6 p-4 bg-pink-50 rounded-xl border border-pink-200">
          <form onSubmit={handleGiveFlower}>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Write a beautiful message with your flowers..."
              className="w-full p-3 border border-pink-300 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
              rows={3}
              maxLength={500}
              disabled={submitting}
            />
            <div className="flex justify-between items-center mt-3">
              <span className="text-xs text-gray-500">
                {message.length}/500 characters
              </span>
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={() => {
                    setShowGiveForm(false)
                    setMessage('')
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 text-sm"
                  disabled={submitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={!message.trim() || submitting}
                  className="px-4 py-2 bg-pink-600 text-white rounded-lg text-sm font-medium hover:bg-pink-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Giving...' : 'Give Flowers 🌹'}
                </button>
              </div>
            </div>
          </form>
        </div>
      )}

      {/* Flowers Display */}
      {flowers.length > 0 ? (
        <div className="space-y-6">
          {flowers.map((flower) => (
            <div key={flower.id} className="relative">
              {/* Rose decoration */}
              <div className="absolute -left-2 top-0 text-2xl">🌹</div>
              
              <div className="ml-8 p-4 bg-gradient-to-r from-pink-50 to-rose-50 rounded-xl border border-pink-200">
                <div className="flex items-start gap-4">
                  <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center overflow-hidden flex-shrink-0">
                    {flower.giver.avatar ? (
                      <img
                        src={flower.giver.avatar}
                        alt={flower.giver.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <span className="text-sm font-serif text-gray-500">
                        {flower.giver.name.charAt(0).toUpperCase()}
                      </span>
                    )}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <span className="font-medium text-gray-900">{flower.giver.name}</span>
                      <span className="text-gray-400">•</span>
                      <span className="text-gray-500 text-sm">{formatDate(flower.created_at)}</span>
                    </div>
                    
                    <p className="text-gray-700 leading-relaxed italic">
                      "{flower.message}"
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🌹</div>
          <h3 className="text-lg font-serif text-gray-800 mb-2">
            {isOwnProfile ? 'No flowers yet' : 'Be the first to give flowers'}
          </h3>
          <p className="text-gray-600">
            {isOwnProfile 
              ? 'Creators you connect with can leave you beautiful flowers here'
              : 'Show your appreciation with a beautiful message'
            }
          </p>
          
          {!isOwnProfile && (
            <button
              onClick={() => setShowGiveForm(true)}
              className="mt-4 bg-pink-600 text-white px-6 py-3 rounded-xl font-medium hover:bg-pink-700 transition-colors"
            >
              Give First Flowers 🌹
            </button>
          )}
        </div>
      )}
    </div>
  )
}
