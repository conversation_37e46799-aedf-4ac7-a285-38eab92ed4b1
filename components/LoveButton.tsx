"use client"

import { useState, useEffect } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface LoveButtonProps {
  entryId: string
  initialLoveCount: number
  userId?: string
}

export function LoveButton({ entryId, initialLoveCount, userId }: LoveButtonProps) {
  const [loveCount, setLoveCount] = useState(initialLoveCount)
  const [isLoved, setIsLoved] = useState(false)
  const [loading, setLoading] = useState(false)
  const supabase = createSupabaseClient()

  // Check if user has already loved this post
  useEffect(() => {
    if (userId) {
      checkIfLoved()
    }
  }, [userId, entryId])

  const checkIfLoved = async () => {
    if (!userId) return

    try {
      const { data, error } = await supabase
        .from('loves')
        .select('id')
        .eq('user_id', userId)
        .eq('diary_entry_id', entryId)
        .single()

      if (!error && data) {
        setIsLoved(true)
      }
    } catch {
      // User hasn't loved this post
      setIsLoved(false)
    }
  }

  const handleLove = async () => {
    if (!userId || loading) return

    console.log('Love button clicked:', { userId, entryId, isLoved })
    setLoading(true)

    try {
      if (isLoved) {
        // Remove love
        console.log('Removing love...')
        const { data, error } = await supabase
          .from('loves')
          .delete()
          .eq('user_id', userId)
          .eq('diary_entry_id', entryId)

        console.log('Delete result:', { data, error })
        if (!error) {
          setIsLoved(false)
          setLoveCount(prev => prev - 1)
          console.log('Love removed successfully')
        } else {
          alert(`Failed to remove love: ${error.message}`)
        }
      } else {
        // Add love
        console.log('Adding love...')
        const { data, error } = await supabase
          .from('loves')
          .insert({
            user_id: userId,
            diary_entry_id: entryId
          })

        console.log('Insert result:', { data, error })
        if (!error) {
          setIsLoved(true)
          setLoveCount(prev => prev + 1)
          console.log('Love added successfully')
        } else {
          alert(`Failed to add love: ${error.message}`)
        }
      }
    } catch (error) {
      console.error('Error toggling love:', error)
      alert(`Love button error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <button
      onClick={handleLove}
      disabled={!userId || loading}
      className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
        isLoved
          ? 'bg-red-50 text-red-600 hover:bg-red-100'
          : 'bg-gray-50 text-gray-600 hover:bg-gray-100'
      } ${!userId ? 'opacity-50 cursor-not-allowed' : 'hover:scale-105'}`}
      title={!userId ? 'Sign in to love this post' : isLoved ? 'Remove love' : 'Love this post'}
    >
      <span className={`text-lg ${isLoved ? 'animate-pulse' : ''}`}>
        {isLoved ? '❤️' : '🤍'}
      </span>
      <span className="text-sm">
        {loveCount === 0 && !isLoved
          ? 'Love this'
          : `${loveCount} ${loveCount === 1 ? 'love' : 'loves'}`
        }
      </span>
    </button>
  )
}
