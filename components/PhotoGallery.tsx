"use client"

import { useState, useEffect } from "react"

interface Photo {
  id: string
  url: string
  alt_text: string
}

interface PhotoGalleryProps {
  photos: Photo[]
}

export function PhotoGallery({ photos }: PhotoGalleryProps) {
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)

  const handlePhotoClick = (photo: Photo) => {
    setSelectedPhoto(photo)
  }

  const closeModal = () => {
    setSelectedPhoto(null)
  }

  // Close modal with Escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && selectedPhoto) {
        closeModal()
      }
    }

    if (selectedPhoto) {
      document.addEventListener('keydown', handleKeyDown)
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [selectedPhoto])

  if (!photos || photos.length === 0) {
    return null
  }

  return (
    <>
      <div className="mt-8 space-y-4">
        {photos.map((photo) => (
          <div key={photo.id} className="w-full">
            <img
              src={photo.url}
              alt={photo.alt_text}
              className="w-full max-w-md mx-auto rounded-lg shadow-sm cursor-pointer hover:shadow-md transition-shadow"
              style={{ maxHeight: '250px', objectFit: 'contain' }}
              onClick={() => handlePhotoClick(photo)}
              title="Click to view full size"
            />
          </div>
        ))}
      </div>

      {/* Modal for full-size image */}
      {selectedPhoto && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={closeModal}
        >
          <div className="relative max-w-4xl max-h-full">
            <img
              src={selectedPhoto.url}
              alt={selectedPhoto.alt_text}
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 bg-black bg-opacity-50 text-white rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75 transition-colors"
              title="Close"
            >
              ×
            </button>
          </div>
        </div>
      )}
    </>
  )
}
