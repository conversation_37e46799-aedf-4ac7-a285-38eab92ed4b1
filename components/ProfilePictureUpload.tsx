"use client"

import { useState, useRef } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"

interface ProfilePictureUploadProps {
  currentPictureUrl?: string
  onPictureChange?: (url: string) => void
  userId: string
}

export function ProfilePictureUpload({ 
  currentPictureUrl, 
  onPictureChange, 
  userId 
}: ProfilePictureUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState("")
  const [previewUrl, setPreviewUrl] = useState(currentPictureUrl || "")
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  const supabase = createSupabaseClient()

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file
    if (!file.type.startsWith('image/')) {
      setError("Please select an image file")
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setError("Image must be smaller than 5MB")
      return
    }

    setUploading(true)
    setError("")

    try {
      // Create unique filename
      const fileExt = file.name.split('.').pop()
      const fileName = `${userId}/profile.${fileExt}`

      // Delete old profile picture if it exists
      if (currentPictureUrl) {
        const oldPath = currentPictureUrl.split('/').pop()
        if (oldPath) {
          await supabase.storage
            .from('profile-pictures')
            .remove([`${userId}/${oldPath}`])
        }
      }

      // Upload new profile picture
      const { error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(fileName, file, {
          upsert: true // Replace if exists
        })

      if (uploadError) {
        throw new Error(`Upload failed: ${uploadError.message}`)
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(fileName)

      // Update user profile in database
      const { error: updateError } = await supabase
        .from('users')
        .update({ profile_picture_url: publicUrl })
        .eq('id', userId)

      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`)
      }

      setPreviewUrl(publicUrl)
      onPictureChange?.(publicUrl)

    } catch (err: any) {
      setError(err.message || "Failed to upload profile picture")
    } finally {
      setUploading(false)
    }
  }

  const handleRemovePicture = async () => {
    if (!currentPictureUrl) return

    setUploading(true)
    setError("")

    try {
      // Remove from storage
      const oldPath = currentPictureUrl.split('/').pop()
      if (oldPath) {
        await supabase.storage
          .from('profile-pictures')
          .remove([`${userId}/${oldPath}`])
      }

      // Update database
      const { error: updateError } = await supabase
        .from('users')
        .update({ profile_picture_url: null })
        .eq('id', userId)

      if (updateError) {
        throw new Error(`Failed to update profile: ${updateError.message}`)
      }

      setPreviewUrl("")
      onPictureChange?.("")

    } catch (err: any) {
      setError(err.message || "Failed to remove profile picture")
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      {/* Current/Preview Picture */}
      <div className="flex items-center gap-4">
        <div className="w-20 h-20 rounded-full overflow-hidden bg-gray-100 flex items-center justify-center">
          {previewUrl ? (
            <img
              src={previewUrl}
              alt="Profile picture"
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="text-gray-400 text-2xl">👤</div>
          )}
        </div>
        
        <div className="flex flex-col gap-2">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={uploading}
            className="bg-gray-800 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700 disabled:opacity-50 transition-colors text-sm"
          >
            {uploading ? "Uploading..." : previewUrl ? "Change Picture" : "Upload Picture"}
          </button>
          
          {previewUrl && (
            <button
              onClick={handleRemovePicture}
              disabled={uploading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-red-700 disabled:opacity-50 transition-colors text-sm"
            >
              Remove Picture
            </button>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Guidelines */}
      <div className="text-xs text-gray-500">
        <p>• Maximum file size: 5MB</p>
        <p>• Supported formats: JPG, PNG, GIF</p>
        <p>• Recommended: Square images work best</p>
      </div>
    </div>
  )
}
