'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase/client'

export function ReaderRedirect() {
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    const checkUserAndRedirect = async () => {
      try {
        const { data: { user } } = await supabase.auth.getUser()
        
        if (user) {
          // Get user profile to check role
          const { data: profile } = await supabase
            .from('users')
            .select('role')
            .eq('id', user.id)
            .single()

          // If user is a subscriber (reader), redirect to timeline
          if (profile?.role === 'subscriber') {
            router.push('/timeline')
          }
        }
      } catch (error) {
        // Ignore errors, just stay on homepage
        console.log('User check failed:', error)
      }
    }

    checkUserAndRedirect()
  }, [router, supabase])

  return null // This component doesn't render anything
}
