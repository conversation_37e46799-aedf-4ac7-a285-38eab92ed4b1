"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

interface StripeConnectButtonProps {
  isConnected: boolean
  onboardingComplete: boolean
}

export function StripeConnectButton({ isConnected, onboardingComplete }: StripeConnectButtonProps) {
  const [loading, setLoading] = useState(false)

  const handleConnect = async () => {
    setLoading(true)
    
    try {
      const response = await fetch('/api/stripe/connect', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      })

      const data = await response.json()

      if (data.url) {
        window.location.href = data.url
      } else {
        console.error('No URL returned from Stripe Connect')
      }
    } catch (error) {
      console.error('Error connecting to Stripe:', error)
    } finally {
      setLoading(false)
    }
  }

  if (isConnected && onboardingComplete) {
    return (
      <div className="flex items-center gap-2">
        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
        <span className="text-sm text-green-600 font-medium">
          Stripe Connected
        </span>
      </div>
    )
  }

  if (isConnected && !onboardingComplete) {
    return (
      <Button
        onClick={handleConnect}
        isLoading={loading}
        className="bg-blue-600 text-white hover:bg-blue-700"
      >
        Complete Stripe Setup
      </Button>
    )
  }

  return (
    <Button
      onClick={handleConnect}
      isLoading={loading}
      className="bg-blue-600 text-white hover:bg-blue-700"
    >
      Connect Stripe Account
    </Button>
  )
}
