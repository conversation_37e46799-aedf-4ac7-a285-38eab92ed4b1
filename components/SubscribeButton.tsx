"use client"

import { useState } from "react"

interface SubscribeButtonProps {
  writerId: string
  price?: number
}

export function SubscribeButton({ writerId }: SubscribeButtonProps) {
  const [loading, setLoading] = useState(false)

  const handleSubscribe = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ writerId }),
      })
      const data = await response.json()
      if (data.url) {
        window.location.href = data.url
      } else {
        alert(data.error || 'Failed to create subscription')
      }
    } catch {
      alert('Failed to create subscription')
    } finally {
      setLoading(false)
    }
  }

  return (
    <button 
      onClick={handleSubscribe}
      disabled={loading}
      className="bg-gray-800 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-700 transition-colors disabled:opacity-50"
    >
      {loading ? 'Loading...' : 'Subscribe'}
    </button>
  )
}