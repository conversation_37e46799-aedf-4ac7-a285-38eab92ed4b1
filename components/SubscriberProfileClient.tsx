'use client';

import { useState } from 'react';
import Image from 'next/image';
import { createBrowserClient } from '@supabase/ssr';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import RecommendationModal from './RecommendationModal';

type Profile = {
  id: string;
  name: string | null;
  avatar: string | null;
  bio: string | null;
  profile_picture_url: string | null;
  social_twitter: string | null;
  social_instagram: string | null;
  social_website: string | null;
  flower_count: number;
  created_at: string;
};

type Flower = {
  id: string;
  message: string;
  created_at: string;
  giver_id: string;
  giver: {
    name: string;
    avatar: string;
  } | null;
};

type FavoriteCreator = {
  id: string;
  created_at: string;
  writer: {
    id: string;
    name: string;
    avatar: string;
    profile_picture_url: string;
    bio: string;
  } | null;
};

type Props = {
  profile: Profile;
  flowers: Flower[];
  favoriteCreators: FavoriteCreator[];
};

export default function SubscriberProfileClient({ profile, flowers: initialFlowers, favoriteCreators }: Props) {
  const [flowers, setFlowers] = useState(initialFlowers);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPhotoModal, setShowPhotoModal] = useState(false);
  const [showSocialModal, setShowSocialModal] = useState(false);
  const [showRecommendationsModal, setShowRecommendationsModal] = useState(false);
  
  // Social links state
  const [socialTwitter, setSocialTwitter] = useState(profile.social_twitter || '');
  const [socialInstagram, setSocialInstagram] = useState(profile.social_instagram || '');
  const [socialWebsite, setSocialWebsite] = useState(profile.social_website || '');
  const [isSavingSocial, setIsSavingSocial] = useState(false);
  
  // Photo upload state
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [isUploadingPhoto, setIsUploadingPhoto] = useState(false);
  const [isUploadingProfilePhoto, setIsUploadingProfilePhoto] = useState(false);
  const [navigatingToCreator, setNavigatingToCreator] = useState<string | null>(null);
  
  const supabase = createBrowserClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  );

  const handleGiveFlower = async () => {
    if (!message.trim()) {
      alert('Please enter a message for the flower.');
      return;
    }

    setIsSubmitting(true);
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      alert('You must be logged in to give a flower.');
      setIsSubmitting(false);
      return;
    }

    const { data, error } = await supabase
      .from('flowers')
      .insert({
        receiver_id: profile.id,
        giver_id: user.id,
        message: message.trim(),
      })
      .select(`
        *,
        giver:giver_id (
          name,
          avatar
        )
      `)
      .single();

    if (error) {
      console.error('Error giving flower:', error);
      alert(error.message);
    } else if (data) {
      const newFlower = data as Flower;
      setFlowers([newFlower, ...flowers]);
      setMessage('');
    }

    setIsSubmitting(false);
  };

  const handleSaveSocialLinks = async () => {
    setIsSavingSocial(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('You must be logged in to update your profile.');
        return;
      }

      const { error } = await supabase
        .from('users')
        .update({
          social_twitter: socialTwitter.trim() || null,
          social_instagram: socialInstagram.trim() || null,
          social_website: socialWebsite.trim() || null,
        })
        .eq('id', user.id);

      if (error) {
        throw error;
      }

      alert('Social links updated successfully!');
      setShowSocialModal(false);
      // Refresh the page to show updated data
      window.location.reload();
    } catch (error) {
      console.error('Error updating social links:', error);
      alert('Failed to update social links. Please try again.');
    } finally {
      setIsSavingSocial(false);
    }
  };

  const handlePhotoFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 2 * 1024 * 1024) { // Reduced to 2MB for base64 storage
        alert('File size must be less than 2MB');
        return;
      }
      
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      setPhotoFile(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        setPhotoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleUploadPhoto = async () => {
    if (!photoFile) {
      alert('Please select a photo first');
      return;
    }

    setIsUploadingPhoto(true);
    setIsUploadingProfilePhoto(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        alert('You must be logged in to update your profile.');
        return;
      }

      // Upload to Supabase Storage - path must match RLS policy
      const fileExt = photoFile.name.split('.').pop();
      const fileName = `profile.${fileExt}`;
      const filePath = `${user.id}/${fileName}`; // Policy requires: user_id/filename

      console.log('Uploading file:', {
        filePath,
        fileSize: photoFile.size,
        fileType: photoFile.type,
        userId: user.id
      });

      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('profile-pictures')
        .upload(filePath, photoFile, { 
          upsert: true,
          cacheControl: '3600'
        });

      if (uploadError) {
        console.error('Upload error details:', {
          message: uploadError.message,
          error: uploadError,
          filePath,
          fileSize: photoFile.size,
          fileType: photoFile.type
        });
        throw new Error(`Upload failed: ${uploadError.message}`);
      }

      console.log('Upload successful:', uploadData);

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('profile-pictures')
        .getPublicUrl(filePath);

      console.log('Public URL:', urlData.publicUrl);

      // Update user profile
      const { error: updateError } = await supabase
        .from('users')
        .update({ 
          profile_picture_url: urlData.publicUrl,
          avatar: urlData.publicUrl // Also update legacy avatar field
        })
        .eq('id', user.id);

      if (updateError) {
        console.error('Database update error:', updateError);
        throw updateError;
      }

      alert('Profile photo updated successfully!');
      setShowPhotoModal(false);
      setPhotoFile(null);
      setPhotoPreview(null);
      // Refresh the page to show updated photo
      window.location.reload();
    } catch (error: any) {
      console.error('Error uploading photo:', error);
      alert(`Failed to upload photo: ${error?.message || 'Unknown error'}`);
    } finally {
      setIsUploadingPhoto(false);
      setIsUploadingProfilePhoto(false);
    }
  };


  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-pink-50 to-orange-50">
      {/* Hero Section */}
      <div className="relative bg-gradient-to-r from-purple-600 via-pink-600 to-orange-500 text-white">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4 py-12 md:py-20">
          <div className="flex flex-col md:flex-row items-center md:items-start gap-6 md:gap-8">
            {/* Avatar */}
            <div className="flex-shrink-0">
              <div className="relative">
                {(profile.profile_picture_url || profile.avatar) ? (
                  <Image
                    src={profile.profile_picture_url || profile.avatar || ''}
                    alt={profile.name || 'User avatar'}
                    width={120}
                    height={120}
                    className={`rounded-full border-4 border-white/20 shadow-2xl object-cover transition-opacity ${
                      isUploadingProfilePhoto ? 'opacity-50' : ''
                    }`}
                  />
                ) : (
                  <div className={`w-[120px] h-[120px] rounded-full bg-white/10 border-4 border-white/20 flex items-center justify-center transition-opacity ${
                    isUploadingProfilePhoto ? 'opacity-50' : ''
                  }`}>
                    <span className="text-4xl font-bold">{profile.name?.[0]?.toUpperCase() || '?'}</span>
                  </div>
                )}
                
                {/* Loading overlay during upload */}
                {isUploadingProfilePhoto && (
                  <div className="absolute inset-0 rounded-full bg-black/30 flex items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
                  </div>
                )}
                
                {/* Edit badge - only show when not uploading */}
                {!isUploadingProfilePhoto && (
                  <button
                    onClick={() => setShowPhotoModal(true)}
                    className="absolute -bottom-2 -right-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg transition-colors"
                    title="Change photo"
                  >
                    <span className="text-lg font-bold">+</span>
                  </button>
                )}
              </div>
            </div>

            {/* Profile Info */}
            <div className="text-center md:text-left flex-1">
              <h1 className="text-3xl md:text-4xl font-bold mb-2">{profile.name}</h1>
              <p className="text-lg text-white/90 mb-4">Subscriber</p>
              {profile.bio && (
                <p className="text-white/80 text-base md:text-lg max-w-2xl">{profile.bio}</p>
              )}
              
              {/* Social Links */}
              {(profile.social_twitter || profile.social_instagram || profile.social_website) && (
                <div className="flex justify-center md:justify-start gap-4 mt-4">
                  {profile.social_twitter && (
                    <a
                      href={`https://twitter.com/${profile.social_twitter}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-3 rounded-full transition-colors"
                      title="Twitter"
                    >
                      <span className="text-lg">🐦</span>
                    </a>
                  )}
                  {profile.social_instagram && (
                    <a
                      href={`https://instagram.com/${profile.social_instagram}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-3 rounded-full transition-colors"
                      title="Instagram"
                    >
                      <span className="text-lg">📷</span>
                    </a>
                  )}
                  {profile.social_website && (
                    <a
                      href={profile.social_website.startsWith('http') ? profile.social_website : `https://${profile.social_website}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-white/10 hover:bg-white/20 text-white p-3 rounded-full transition-colors"
                      title="Website"
                    >
                      <span className="text-lg">🌐</span>
                    </a>
                  )}
                </div>
              )}
              
              {/* Stats */}
              <div className="flex justify-center md:justify-start gap-4 sm:gap-8 mt-6">
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold">{favoriteCreators.length}</div>
                  <div className="text-xs sm:text-sm text-white/80">Recommendations</div>
                </div>
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold">{profile.flower_count || flowers.length}</div>
                  <div className="text-xs sm:text-sm text-white/80">Flowers Received</div>
                </div>
                <div className="text-center">
                  <div className="text-xl sm:text-2xl font-bold">{new Date(profile.created_at).getFullYear()}</div>
                  <div className="text-xs sm:text-sm text-white/80">Member Since</div>
                </div>
              </div>
            </div>

            {/* Edit Profile Button - TODO: Add logic to show only for own profile */}
            <div className="flex-shrink-0 w-full md:w-auto">
              <Button className="w-full md:w-auto bg-white/10 hover:bg-white/20 text-white border border-white/20 px-6 py-3">
                ⚙️ Edit Profile
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Recommended Creators Section */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-xl">
                  ⭐ Recommended Creators
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm">
                    {favoriteCreators.length}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {favoriteCreators.length > 0 ? (
                  <div className="grid grid-cols-1 gap-4">
                    {favoriteCreators.map((favorite) => (
                      <Card 
                        key={favorite.id} 
                        className={`border border-blue-100 hover:shadow-md transition-shadow cursor-pointer relative ${
                          navigatingToCreator === favorite.writer?.id ? 'opacity-75' : ''
                        }`}
                        onClick={() => {
                          if (favorite.writer?.id) {
                            setNavigatingToCreator(favorite.writer.id);
                            window.location.href = `/u/${favorite.writer.id}`;
                          }
                        }}
                      >
                        <CardContent className="p-4">
                          {/* Loading overlay */}
                          {navigatingToCreator === favorite.writer?.id && (
                            <div className="absolute inset-0 bg-white/50 flex items-center justify-center rounded-lg">
                              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                            </div>
                          )}
                          <div className="flex items-center gap-3">
                            {(favorite.writer?.profile_picture_url || favorite.writer?.avatar) ? (
                              <Image
                                src={favorite.writer.profile_picture_url || favorite.writer.avatar || ''}
                                alt={favorite.writer.name || 'Creator avatar'}
                                width={56}
                                height={56}
                                className="rounded-full border-2 border-blue-100 object-cover"
                              />
                            ) : (
                              <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center border-2 border-blue-100">
                                <span className="text-lg font-semibold text-blue-700">
                                  {favorite.writer?.name?.[0]?.toUpperCase() || '?'}
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <p className="font-semibold text-gray-900 truncate text-lg">{favorite.writer?.name}</p>
                              {favorite.writer?.bio && (
                                <p className="text-sm text-gray-500 line-clamp-2 mt-1">{favorite.writer.bio}</p>
                              )}
                              <div className="flex items-center gap-2 mt-2">
                                <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                                  Recommended
                                </span>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">⭐</div>
                    <p className="text-gray-500 text-lg mb-2">No recommendations yet</p>
                    <p className="text-gray-400">Share your favorite creators with the community!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Social Links Section */}
            {(profile.social_twitter || profile.social_instagram || profile.social_website) && (
              <Card className="border-0 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-green-50 to-teal-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2 text-xl">
                    🔗 Social Links
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="grid grid-cols-1 gap-4">
                    {profile.social_twitter && (
                      <a
                        href={`https://twitter.com/${profile.social_twitter}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors border border-blue-200"
                      >
                        <span className="text-2xl">🐦</span>
                        <div>
                          <p className="font-semibold text-blue-900">Twitter</p>
                          <p className="text-sm text-blue-700">@{profile.social_twitter}</p>
                        </div>
                      </a>
                    )}
                    {profile.social_instagram && (
                      <a
                        href={`https://instagram.com/${profile.social_instagram}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-4 bg-pink-50 hover:bg-pink-100 rounded-lg transition-colors border border-pink-200"
                      >
                        <span className="text-2xl">📷</span>
                        <div>
                          <p className="font-semibold text-pink-900">Instagram</p>
                          <p className="text-sm text-pink-700">@{profile.social_instagram}</p>
                        </div>
                      </a>
                    )}
                    {profile.social_website && (
                      <a
                        href={profile.social_website.startsWith('http') ? profile.social_website : `https://${profile.social_website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center gap-3 p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors border border-green-200"
                      >
                        <span className="text-2xl">🌐</span>
                        <div>
                          <p className="font-semibold text-green-900">Website</p>
                          <p className="text-sm text-green-700 truncate">{profile.social_website}</p>
                        </div>
                      </a>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Flowers Received Section */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-xl">
                  🌸 Flowers Received
                  <span className="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-sm">
                    {flowers.length}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {flowers.length > 0 ? (
                  <div className="space-y-4">
                    {flowers.slice(0, 3).map((flower) => (
                      <Card key={flower.id} className="border border-pink-100">
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            {flower.giver?.avatar ? (
                              <Image
                                src={flower.giver.avatar}
                                alt={flower.giver.name || 'Giver avatar'}
                                width={40}
                                height={40}
                                className="rounded-full border-2 border-pink-100"
                              />
                            ) : (
                              <div className="w-10 h-10 rounded-full bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center border-2 border-pink-100">
                                <span className="text-xs font-semibold text-pink-700">
                                  {flower.giver?.name?.[0]?.toUpperCase() || '?'}
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <p className="font-medium text-gray-900 text-sm">{flower.giver?.name}</p>
                                <span>🌸</span>
                              </div>
                              <p className="text-xs text-gray-500 mb-1">
                                {new Date(flower.created_at).toLocaleDateString()}
                              </p>
                              <p className="text-sm text-gray-700 break-words">{flower.message}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                    {flowers.length > 3 && (
                      <div className="text-center pt-2">
                        <Button variant="outline" size="sm">
                          View all {flowers.length} flowers
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">🌸</div>
                    <p className="text-gray-500">No flowers received yet</p>
                    <p className="text-gray-400 text-sm">Be the first to send a flower!</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Old Flowers Section - Remove this */}
            {false && flowers.length > 0 && (
              <Card className="border-0 shadow-lg">
                <CardHeader className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-t-lg">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    🌸 Recent Flowers
                    <span className="bg-pink-100 text-pink-800 px-2 py-1 rounded-full text-sm">
                    {flowers.length}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                {flowers.length > 0 ? (
                  <div className="space-y-4">
                    {flowers.map((flower) => (
                      <Card key={flower.id} className="border border-pink-100 hover:shadow-md transition-shadow">
                        <CardContent className="p-4">
                          <div className="flex items-start gap-3">
                            {flower.giver?.avatar ? (
                              <Image
                                src={flower.giver.avatar}
                                alt={flower.giver.name || 'Giver avatar'}
                                width={48}
                                height={48}
                                className="rounded-full border-2 border-pink-100"
                              />
                            ) : (
                              <div className="w-12 h-12 rounded-full bg-gradient-to-br from-pink-100 to-purple-100 flex items-center justify-center border-2 border-pink-100">
                                <span className="text-sm font-semibold text-pink-700">
                                  {flower.giver?.name?.[0]?.toUpperCase() || '?'}
                                </span>
                              </div>
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <p className="font-semibold text-gray-900">{flower.giver?.name}</p>
                                <span className="text-xl">🌸</span>
                              </div>
                              <p className="text-sm text-gray-500 mb-2">
                                {new Date(flower.created_at).toLocaleDateString('en-US', {
                                  month: 'short',
                                  day: 'numeric',
                                  year: 'numeric'
                                })}
                              </p>
                              <p className="text-gray-700 break-words">{flower.message}</p>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <div className="text-6xl mb-4">🌸</div>
                    <p className="text-gray-500 text-lg mb-2">No flowers yet</p>
                    <p className="text-gray-400">Be the first to send a flower!</p>
                  </div>
                )}
              </CardContent>
            </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6 order-first lg:order-last">
            {/* Profile Actions Card */}
            <Card className="border-0 shadow-lg lg:sticky lg:top-4">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-lg">
                  ⚙️ Profile Actions
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6 space-y-3">
                <Button 
                  onClick={() => setShowPhotoModal(true)}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                >
                  📸 Update Photo
                </Button>
                <Button 
                  onClick={() => setShowSocialModal(true)}
                  className="w-full bg-purple-600 hover:bg-purple-700 text-white"
                >
                  🔗 Edit Social Links
                </Button>
                <Button 
                  onClick={() => setShowRecommendationsModal(true)}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                >
                  ⭐ Manage Recommendations
                </Button>
              </CardContent>
            </Card>

            {/* Send Flower Card - Secondary */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-pink-50 to-purple-50 rounded-t-lg">
                <CardTitle className="flex items-center gap-2 text-lg">
                  🌸 Send a Flower
                </CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <Textarea
                  placeholder="Write a heartfelt message..."
                  value={message}
                  onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => setMessage(e.target.value)}
                  className="mb-4 min-h-[80px] border-pink-200 focus:border-pink-400 focus:ring-pink-300"
                  maxLength={280}
                />
                <div className="text-sm text-gray-400 mb-3">
                  {message.length}/280 characters
                </div>
                <Button 
                  onClick={handleGiveFlower} 
                  disabled={isSubmitting || !message.trim()}
                  className="w-full bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600 text-white border-0 shadow-lg disabled:opacity-50"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      🌸 Send Flower
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>

            {/* Profile Info Card */}
            <Card className="border-0 shadow-lg">
              <CardHeader className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-t-lg">
                <CardTitle className="text-lg">About</CardTitle>
              </CardHeader>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <span className="text-lg">👤</span>
                    <span className="text-gray-600">Subscriber</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-lg">⭐</span>
                    <span className="text-gray-600">{favoriteCreators.length} recommendations</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <span className="text-lg">📅</span>
                    <span className="text-gray-600">
                      Member since {new Date(profile.created_at).getFullYear()}
                    </span>
                  </div>
                  {(profile.social_twitter || profile.social_instagram || profile.social_website) && (
                    <div className="pt-3 border-t border-gray-200">
                      <p className="text-sm font-medium text-gray-700 mb-2">Social Links</p>
                      <div className="flex gap-2">
                        {profile.social_twitter && (
                          <a
                            href={`https://twitter.com/${profile.social_twitter}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-blue-100 hover:bg-blue-200 text-blue-700 p-2 rounded-lg transition-colors"
                            title="Twitter"
                          >
                            🐦
                          </a>
                        )}
                        {profile.social_instagram && (
                          <a
                            href={`https://instagram.com/${profile.social_instagram}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-pink-100 hover:bg-pink-200 text-pink-700 p-2 rounded-lg transition-colors"
                            title="Instagram"
                          >
                            📷
                          </a>
                        )}
                        {profile.social_website && (
                          <a
                            href={profile.social_website.startsWith('http') ? profile.social_website : `https://${profile.social_website}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-green-100 hover:bg-green-200 text-green-700 p-2 rounded-lg transition-colors"
                            title="Website"
                          >
                            🌐
                          </a>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Photo Upload Modal */}
      {showPhotoModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Update Profile Photo</h3>
            
            {/* File Upload */}
            <div className="mb-4">
              <input
                type="file"
                accept="image/*"
                onChange={handlePhotoFileChange}
                className="w-full p-2 border border-gray-300 rounded-lg"
              />
              <p className="text-xs text-gray-500 mt-1">Max 2MB. JPG, PNG, GIF supported.</p>
            </div>

            {/* Photo Preview */}
            {photoPreview && (
              <div className="mb-4">
                <p className="text-sm font-medium mb-2">Preview:</p>
                <img
                  src={photoPreview}
                  alt="Preview"
                  className="w-24 h-24 rounded-full object-cover border-2 border-gray-200"
                />
              </div>
            )}

            <div className="flex gap-2">
              <Button 
                onClick={() => {
                  setShowPhotoModal(false);
                  setPhotoFile(null);
                  setPhotoPreview(null);
                }}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                onClick={handleUploadPhoto}
                disabled={!photoFile || isUploadingPhoto}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
              >
                {isUploadingPhoto ? 'Uploading...' : 'Upload Photo'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Social Links Modal */}
      {showSocialModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Edit Social Links</h3>
            
            <div className="space-y-4">
              {/* Twitter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  🐦 Twitter Username
                </label>
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm mr-2">@</span>
                  <input
                    type="text"
                    value={socialTwitter}
                    onChange={(e) => setSocialTwitter(e.target.value)}
                    placeholder="username"
                    className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Instagram */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  📷 Instagram Username
                </label>
                <div className="flex items-center">
                  <span className="text-gray-500 text-sm mr-2">@</span>
                  <input
                    type="text"
                    value={socialInstagram}
                    onChange={(e) => setSocialInstagram(e.target.value)}
                    placeholder="username"
                    className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500"
                  />
                </div>
              </div>

              {/* Website */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  🌐 Website URL
                </label>
                <input
                  type="url"
                  value={socialWebsite}
                  onChange={(e) => setSocialWebsite(e.target.value)}
                  placeholder="https://your-website.com"
                  className="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500"
                />
              </div>
            </div>

            <div className="flex gap-2 mt-6">
              <Button 
                onClick={() => {
                  setShowSocialModal(false);
                  // Reset to original values
                  setSocialTwitter(profile.social_twitter || '');
                  setSocialInstagram(profile.social_instagram || '');
                  setSocialWebsite(profile.social_website || '');
                }}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button 
                onClick={handleSaveSocialLinks}
                disabled={isSavingSocial}
                className="flex-1 bg-purple-600 hover:bg-purple-700 text-white disabled:opacity-50"
              >
                {isSavingSocial ? 'Saving...' : 'Save Links'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Recommendations Modal */}
      {showRecommendationsModal && (
        <RecommendationModal onClose={() => setShowRecommendationsModal(false)} />
      )}
    </div>
  );
}
