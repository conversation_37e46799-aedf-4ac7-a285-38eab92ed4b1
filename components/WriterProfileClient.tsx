'use client'

import { useState } from "react"
import Link from "next/link"
import { SubscribeButton } from "@/components/SubscribeButton"
import { FollowButton } from "@/components/FollowButton"
import { SmartTypographyWrapper } from "@/components/SmartTypographyWrapper"

interface DiaryEntry {
  id: string
  title: string
  body_md: string
  is_free: boolean
  created_at: string
}

interface Project {
  id: string
  title: string
  description: string
  cover_image_url: string
  genre: string
  is_complete: boolean
  price_type: 'project' | 'chapters'
  price_amount: number
  total_chapters: number
  total_words: number
  created_at: string
}

interface WriterProfileClientProps {
  writer: any
  diaryEntries: DiaryEntry[]
  projects: Project[]
  hasActiveSubscription: boolean
  isFollowing: boolean
  isOwnProfile: boolean
}

function formatPrice(cents: number) {
  return `$${(cents / 100).toFixed(2)}`
}

function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

export function WriterProfileClient({ 
  writer, 
  diaryEntries, 
  projects, 
  hasActiveSubscription,
  isFollowing, 
  isOwnProfile 
}: WriterProfileClientProps) {
  const [activeTab, setActiveTab] = useState<'diary' | 'books'>('diary')

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/30">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        
        {/* Enhanced Writer Profile Header */}
        <div className="bg-white rounded-3xl p-6 sm:p-8 shadow-xl mb-8 border border-gray-100">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6">
            <div className="flex flex-col sm:flex-row items-center sm:items-start gap-6">
              {/* Profile Picture */}
              <div className="w-24 h-24 sm:w-28 sm:h-28 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center overflow-hidden shadow-lg">
                {writer.writer_avatar ? (
                  <img
                    src={writer.writer_avatar}
                    alt={writer.writer_name || 'Writer avatar'}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <span className="text-3xl sm:text-4xl font-serif text-white font-bold">
                    {(writer.writer_name || 'A').charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
              
              <div className="flex-1 text-center sm:text-left">
                <h1 className="text-2xl sm:text-4xl font-serif mb-3 text-gray-800">
                  {writer.writer_name || 'Anonymous Writer'}
                </h1>
                {writer.writer_bio && (
                  <p className="text-gray-600 font-serif mb-4 leading-relaxed text-sm sm:text-base">
                    {writer.writer_bio}
                  </p>
                )}

                {/* Stats */}
                <div className="flex flex-wrap justify-center sm:justify-start gap-4 sm:gap-6 mb-4 text-sm">
                  <div className="text-center">
                    <div className="font-bold text-lg text-purple-600">{diaryEntries.length}</div>
                    <div className="text-gray-500">Diary Entries</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg text-blue-600">{projects.length}</div>
                    <div className="text-gray-500">Book Projects</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg text-green-600">
                      {(
                        projects.reduce((sum, p) => sum + p.total_words, 0) +
                        diaryEntries.reduce((sum, entry) => sum + entry.body_md.split(' ').length, 0)
                      ).toLocaleString()}
                    </div>
                    <div className="text-gray-500">Total Words</div>
                  </div>
                </div>

                {/* Subscription Status & CTA */}
                {hasActiveSubscription ? (
                  <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                    <p className="text-green-800 font-medium">✓ You're subscribed to this writer</p>
                    <p className="text-green-600 text-sm">You have access to all content</p>
                  </div>
                ) : writer.writer_price_monthly ? (
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <span className="text-lg font-bold text-gray-800">
                      {formatPrice(writer.writer_price_monthly)}/month
                    </span>
                    <SubscribeButton
                      writerId={writer.writer_id}
                      price={writer.writer_price_monthly}
                    />
                  </div>
                ) : (
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    <p className="text-gray-500 text-sm">Free to follow</p>
                    <FollowButton
                      writerId={writer.writer_id}
                      writerName={writer.writer_name}
                      initialIsFollowing={isFollowing}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Edit Button (only for own profile) */}
            {isOwnProfile && (
              <Link
                href="/profile/edit"
                className="bg-gray-800 text-white px-6 py-3 rounded-xl font-medium hover:bg-gray-700 transition-colors self-center sm:self-start"
              >
                ⚙️ Edit Profile
              </Link>
            )}
          </div>
        </div>

        {/* Content Type Tabs */}
        <div className="bg-white rounded-2xl p-2 shadow-lg mb-8 border border-gray-100">
          <div className="flex">
            <button
              onClick={() => setActiveTab('diary')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'diary'
                  ? 'bg-purple-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <span className="flex items-center justify-center gap-2">
                <span>📔</span>
                <span>Diary Entries</span>
                <span className="text-xs opacity-75">({diaryEntries.length})</span>
              </span>
            </button>
            <button
              onClick={() => setActiveTab('books')}
              className={`flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-300 ${
                activeTab === 'books'
                  ? 'bg-blue-600 text-white shadow-lg'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              <span className="flex items-center justify-center gap-2">
                <span>📚</span>
                <span>Book Projects</span>
                <span className="text-xs opacity-75">({projects.length})</span>
              </span>
            </button>
          </div>
        </div>

        {/* Content Display */}
        {activeTab === 'diary' ? (
          <DiaryEntriesSection 
            entries={diaryEntries}
            hasActiveSubscription={hasActiveSubscription}
            writerName={writer.writer_name}
          />
        ) : (
          <BookProjectsSection 
            projects={projects}
            writerName={writer.writer_name}
          />
        )}

        {/* Subscription CTA for non-subscribers */}
        {!hasActiveSubscription && writer.writer_price_monthly && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-3xl p-8 shadow-lg mt-12 text-center border border-purple-100">
            <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-white text-2xl">✨</span>
            </div>
            <h3 className="text-2xl font-serif mb-4 text-gray-800">
              Unlock All Content
            </h3>
            <p className="text-gray-600 font-serif mb-6 text-lg max-w-2xl mx-auto">
              Subscribe to access all of {writer.writer_name}'s diary entries, book projects, and future content
            </p>
            <SubscribeButton 
              writerId={writer.writer_id} 
              price={writer.writer_price_monthly} 
            />
          </div>
        )}
      </div>
    </div>
  )
}

// Diary Entries Section Component
function DiaryEntriesSection({ 
  entries, 
  hasActiveSubscription, 
  writerName 
}: { 
  entries: DiaryEntry[]
  hasActiveSubscription: boolean
  writerName: string
}) {
  if (entries.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📔</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Diary Entries Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any diary entries yet. Check back soon!
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {entries.map((entry) => (
        <div key={entry.id} className="bg-white rounded-2xl p-6 sm:p-8 shadow-lg border border-gray-100 hover:shadow-xl transition-all duration-300">
          <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4 gap-3">
            <h4 className="text-xl sm:text-2xl font-serif text-gray-800 flex-1">
              {entry.title}
            </h4>
            <div className="flex items-center gap-2">
              {entry.is_free && (
                <span className="text-xs text-green-700 font-bold bg-green-100 px-3 py-1 rounded-full border border-green-200">
                  FREE
                </span>
              )}
              <span className="text-xs text-gray-500">
                {formatDate(entry.created_at)}
              </span>
            </div>
          </div>
          
          <div className="text-gray-600 font-serif leading-relaxed mb-6">
            {entry.is_free || hasActiveSubscription ? (
              <SmartTypographyWrapper 
                content={entry.body_md.length > 300 ? entry.body_md.slice(0, 300) + '...' : entry.body_md}
                className="text-gray-700"
              />
            ) : (
              <div className="text-gray-600">
                {entry.body_md.slice(0, 120) + (entry.body_md.length > 120 ? '...' : '')}
              </div>
            )}
          </div>
          
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              {entry.body_md.length} characters • {Math.ceil(entry.body_md.split(' ').length / 200)} min read
            </div>
            
            <Link
              href={`/d/${entry.id}`}
              className="bg-purple-600 text-white px-6 py-2 rounded-xl font-medium hover:bg-purple-700 transition-colors"
            >
              Read Full Story →
            </Link>
          </div>
        </div>
      ))}
    </div>
  )
}

// Book Projects Section Component  
function BookProjectsSection({ 
  projects, 
  writerName 
}: { 
  projects: Project[]
  writerName: string
}) {
  if (projects.length === 0) {
    return (
      <div className="bg-white rounded-2xl p-12 shadow-lg text-center border border-gray-100">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <span className="text-3xl">📚</span>
        </div>
        <h3 className="text-xl font-serif text-gray-800 mb-3">No Book Projects Yet</h3>
        <p className="text-gray-600 font-serif">
          {writerName} hasn't published any book projects yet. Check back soon!
        </p>
      </div>
    )
  }

  return (
    <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {projects.map((project) => (
        <Link
          key={project.id}
          href={`/projects/${project.id}`}
          className="group"
        >
          <div className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 group-hover:scale-[1.02]">
            
            {/* Book Cover - KDP Standard 10:16 ratio */}
            <div className="aspect-[10/16] bg-gradient-to-br from-blue-100 to-purple-100 relative overflow-hidden">
              {project.cover_image_url ? (
                <img
                  src={project.cover_image_url}
                  alt={project.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center">
                  <span className="text-6xl opacity-50">📖</span>
                </div>
              )}
              
              {/* Status Badge */}
              {project.is_complete && (
                <div className="absolute top-3 right-3">
                  <span className="bg-green-500/90 text-white text-xs px-2 py-1 rounded-full font-medium">
                    Complete
                  </span>
                </div>
              )}
            </div>
            
            {/* Project Info */}
            <div className="p-6">
              <h3 className="font-serif text-lg text-gray-800 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors">
                {project.title}
              </h3>
              
              {project.description && (
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {project.description}
                </p>
              )}
              
              <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                <span>{project.total_chapters} chapters</span>
                <span>{project.total_words.toLocaleString()} words</span>
              </div>
              
              {project.genre && (
                <div className="mb-3">
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">
                    {project.genre}
                  </span>
                </div>
              )}
              
              {project.price_amount && (
                <div className="text-sm font-medium text-blue-600">
                  ${(project.price_amount / 100).toFixed(2)} 
                  {project.price_type === 'chapters' ? ' per 30 chapters' : ' full access'}
                </div>
              )}
            </div>
          </div>
        </Link>
      ))}
    </div>
  )
}
