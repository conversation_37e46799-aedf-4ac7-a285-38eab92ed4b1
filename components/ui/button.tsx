import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import InkLoader from "@/components/InkLoader"
import { LoadingSpinner } from "./loading-spinner"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-white transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neutral-950 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 dark:ring-offset-neutral-950 dark:focus-visible:ring-neutral-300 min-h-[48px] active:scale-[0.98] hover:scale-[1.02] shadow-sm hover:shadow-md",
  {
    variants: {
      variant: {
        default: "bg-neutral-900 text-neutral-50 hover:bg-neutral-900/90 dark:bg-neutral-50 dark:text-neutral-900 dark:hover:bg-neutral-50/90",
        destructive:
          "bg-red-500 text-neutral-50 hover:bg-red-500/90 dark:bg-red-900 dark:text-neutral-50 dark:hover:bg-red-900/90",
        outline:
          "border border-neutral-200 bg-white hover:bg-neutral-100 hover:text-neutral-900 dark:border-neutral-800 dark:bg-neutral-950 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
        secondary:
          "bg-neutral-100 text-neutral-900 hover:bg-neutral-100/80 dark:bg-neutral-800 dark:text-neutral-50 dark:hover:bg-neutral-800/80",
        ghost: "hover:bg-neutral-100 hover:text-neutral-900 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
        link: "text-neutral-900 underline-offset-4 hover:underline dark:text-neutral-50",
      },
      size: {
        default: "h-12 px-4 py-2 sm:h-11 sm:px-6",
        sm: "h-10 rounded-lg px-3 sm:h-9",
        lg: "h-14 rounded-xl px-6 sm:h-12 sm:px-8",
        icon: "h-12 w-12 sm:h-11 sm:w-11",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  isLoading?: boolean
  loadingType?: "ink" | "spinner"
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, isLoading = false, children, disabled, ...props }, ref) => {
    // Ensure minimum display time for loader to avoid flash
    const [showLoader, setShowLoader] = React.useState(false)
    const [minDisplayTime, setMinDisplayTime] = React.useState(false)

    React.useEffect(() => {
      if (isLoading) {
        setShowLoader(true)
        setMinDisplayTime(false)

        // Minimum 300ms display time
        const timer = setTimeout(() => {
          setMinDisplayTime(true)
        }, 300)

        return () => clearTimeout(timer)
      } else if (minDisplayTime) {
        setShowLoader(false)
      }
    }, [isLoading, minDisplayTime])

    React.useEffect(() => {
      if (!isLoading && minDisplayTime) {
        setShowLoader(false)
      }
    }, [isLoading, minDisplayTime])

    return (
      <button
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        disabled={isLoading || disabled}
        aria-busy={isLoading}
        style={{ cursor: (isLoading || disabled) ? 'not-allowed' : 'pointer' }}
        {...props}
      >
        {showLoader ? (
          <InkLoader lines={3} className="w-8" />
        ) : (
          children
        )}
      </button>
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }