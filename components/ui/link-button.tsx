"use client"

import * as React from "react"
import Link from "next/link"
import { useRouter, usePathname } from "next/navigation"
import { <PERSON>ton, type ButtonProps } from "@/components/ui/button"
import { LoadingSpinner } from "./loading-spinner"

interface LinkButtonProps extends Omit<ButtonProps, 'onClick'> {
  href: string
  replace?: boolean
  scroll?: boolean
}

export function LinkButton({
  href,
  children,
  replace = false,
  scroll = true,
  className,
  variant,
  size,
  ...props
}: LinkButtonProps) {
  const [isLoading, setIsLoading] = React.useState(false)
  const router = useRouter()
  const pathname = usePathname()

  // Reset loading state when pathname changes
  React.useEffect(() => {
    setIsLoading(false)
  }, [pathname])

  // Auto-reset loading state after timeout as fallback
  React.useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        setIsLoading(false)
      }, 5000) // 5 second fallback

      return () => clearTimeout(timeout)
    }
  }, [isLoading])

  const handleClick = async (e: React.MouseEvent) => {
    e.preventDefault()

    // Don't show loading if we're already on this page
    if (pathname === href) {
      return
    }

    setIsLoading(true)

    try {
      if (replace) {
        router.replace(href, { scroll })
      } else {
        router.push(href, { scroll })
      }
    } catch (error) {
      console.error('Navigation error:', error)
      setIsLoading(false)
    }
  }

  return (
    <Button
      onClick={handleClick}
      disabled={isLoading}
      className={className}
      variant={variant}
      size={size}
      {...props}
    >
      {isLoading ? (
        <div className="flex items-center gap-2">
          <LoadingSpinner size="sm" />
          <span>{children}</span>
        </div>
      ) : (
        children
      )}
    </Button>
  )
}
