import { createSupabaseClient } from '@/lib/supabase/client'

export async function resetAuthState() {
  const supabase = createSupabaseClient()
  
  try {
    // Sign out to clear any invalid tokens
    await supabase.auth.signOut()
    
    // Clear local storage
    localStorage.clear()
    sessionStorage.clear()
    
    // Redirect to home
    window.location.href = '/'
  } catch (error) {
    console.error('Error resetting auth state:', error)
    // Force clear storage and redirect anyway
    localStorage.clear()
    sessionStorage.clear()
    window.location.href = '/'
  }
}

// Auto-reset on invalid refresh token
export function handleAuthError(error: any) {
  if (error?.message?.includes('Invalid Refresh Token') || 
      error?.message?.includes('Refresh Token Not Found')) {
    console.log('Invalid refresh token detected, resetting auth state...')
    resetAuthState()
  }
}
