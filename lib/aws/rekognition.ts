import { RekognitionClient, DetectModerationLabelsCommand, DetectLabelsCommand, DetectTextCommand } from '@aws-sdk/client-rekognition'

// Check if AWS credentials are available
const hasAWSCredentials = !!(process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY)

// Configure AWS Rekognition Client only if credentials are available
let rekognitionClient: RekognitionClient | null = null

if (hasAWSCredentials) {
  rekognitionClient = new RekognitionClient({
    region: process.env.AWS_REGION || 'us-east-1',
    credentials: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    },
  })
}

export interface ModerationResult {
  isAppropriate: boolean
  labels: string[]
  confidence: number
  details: any[]
}

export async function moderateImage(imageUrl: string): Promise<ModerationResult> {
  // Check if AWS is configured
  if (!hasAWSCredentials || !rekognitionClient) {
    console.log('AWS not configured, auto-approving image')
    return {
      isAppropriate: true,
      labels: [],
      confidence: 100,
      details: [{ message: 'AWS not configured, auto-approved' }]
    }
  }

  try {
    console.log('Fetching image for moderation:', imageUrl)

    // Fetch the image
    const response = await fetch(imageUrl)
    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status}`)
    }

    const imageBuffer = await response.arrayBuffer()
    const imageBytes = new Uint8Array(imageBuffer)

    console.log('Image fetched, size:', imageBytes.length, 'bytes')

    // Detect inappropriate content
    const moderationCommand = new DetectModerationLabelsCommand({
      Image: {
        Bytes: imageBytes
      },
      MinConfidence: 60 // Confidence threshold (60%)
    })

    console.log('Sending to AWS Rekognition...')
    const moderationResult = await rekognitionClient.send(moderationCommand)

    // Check for inappropriate content
    const inappropriateLabels = moderationResult.ModerationLabels || []
    const isAppropriate = inappropriateLabels.length === 0

    console.log('AWS Rekognition response received')

    // Get general labels for context
    const labelCommand = new DetectLabelsCommand({
      Image: {
        Bytes: imageBytes
      },
      MaxLabels: 10,
      MinConfidence: 70
    })

    const labelResult = await rekognitionClient.send(labelCommand)
    const generalLabels = labelResult.Labels?.map(label => label.Name || '') || []

    const result = {
      isAppropriate,
      labels: generalLabels,
      confidence: inappropriateLabels.length > 0
        ? Math.max(...inappropriateLabels.map(label => label.Confidence || 0))
        : 100,
      details: inappropriateLabels
    }

    console.log('Moderation complete:', result)
    return result

  } catch (error) {
    console.error('Error moderating image:', error)

    // In case of error, default to approved (not manual review)
    return {
      isAppropriate: true, // Auto-approve on error
      labels: [],
      confidence: 0,
      details: [{ error: 'Failed to process image, auto-approved' }]
    }
  }
}

export async function analyzeImage(imageUrl: string) {
  try {
    const response = await fetch(imageUrl)
    const imageBuffer = await response.arrayBuffer()
    const imageBytes = new Uint8Array(imageBuffer)

    // Detect labels
    const labelCommand = new DetectLabelsCommand({
      Image: {
        Bytes: imageBytes
      },
      MaxLabels: 20,
      MinConfidence: 60
    })

    const labelResult = await rekognitionClient.send(labelCommand)

    // Detect text (if any)
    const textCommand = new DetectTextCommand({
      Image: {
        Bytes: imageBytes
      }
    })

    const textResult = await rekognitionClient.send(textCommand)

    return {
      labels: labelResult.Labels || [],
      text: textResult.TextDetections || []
    }

  } catch (error) {
    console.error('Error analyzing image:', error)
    return {
      labels: [],
      text: []
    }
  }
}
