// Database debugging utilities
import { createSupabaseClient } from '@/lib/supabase/client'

export async function debugProject(projectId: string) {
  const supabase = createSupabaseClient()
  
  console.log('🔍 Debugging project:', projectId)
  
  // Check if project exists
  const { data: project, error: projectError } = await supabase
    .from('projects')
    .select('*')
    .eq('id', projectId)
    .single()
  
  console.log('📁 Project query:', { project, projectError })
  
  if (!project) {
    console.log('❌ Project not found')
    return
  }
  
  // Check if user exists
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('*')
    .eq('id', project.user_id)
    .single()
  
  console.log('👤 User query:', { user, userError })
  
  // Check projects table structure
  const { data: projectsSchema, error: schemaError } = await supabase
    .from('projects')
    .select('*')
    .limit(1)
  
  console.log('📋 Projects table schema (sample):', { projectsSchema, schemaError })
  
  // Check users table structure
  const { data: usersSchema, error: usersSchemaError } = await supabase
    .from('users')
    .select('*')
    .limit(1)
  
  console.log('👥 Users table schema (sample):', { usersSchema, usersSchemaError })
  
  return {
    project,
    user,
    projectError,
    userError
  }
}

// Call this in the browser console to debug
if (typeof window !== 'undefined') {
  (window as any).debugProject = debugProject
}
