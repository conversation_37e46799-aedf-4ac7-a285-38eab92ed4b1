// Push notification client-side utilities
import { createSupabaseClient } from '@/lib/supabase/client'

export interface PushNotificationData {
  title: string
  body: string
  url?: string
  type: 'comment' | 'reply' | 'waitlist' | 'new_chapter'
  data?: any
}

// Check if push notifications are supported
export function isPushSupported(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window
}

// Request notification permission
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!('Notification' in window)) {
    throw new Error('This browser does not support notifications')
  }

  return await Notification.requestPermission()
}

// Register service worker
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration> {
  if (!('serviceWorker' in navigator)) {
    throw new Error('Service workers are not supported')
  }

  const registration = await navigator.serviceWorker.register('/sw.js')
  console.log('Service Worker registered:', registration)
  return registration
}

// Subscribe to push notifications
export async function subscribeToPush(userId: string): Promise<boolean> {
  try {
    const registration = await registerServiceWorker()
    
    // Check if already subscribed
    const existingSubscription = await registration.pushManager.getSubscription()
    if (existingSubscription) {
      console.log('Already subscribed to push notifications')
      return true
    }

    // Request permission
    const permission = await requestNotificationPermission()
    if (permission !== 'granted') {
      console.log('Notification permission denied')
      return false
    }

    // Check if VAPID key is available
    const vapidKey = process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
    if (!vapidKey) {
      console.error('VAPID public key not configured')
      throw new Error('Push notifications not configured')
    }

    // Subscribe to push manager
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array(vapidKey)
    })

    // Save subscription to database
    const supabase = createSupabaseClient()
    const { error } = await supabase
      .from('push_subscriptions')
      .insert({
        user_id: userId,
        subscription: subscription.toJSON(),
        user_agent: navigator.userAgent
      })

    if (error) {
      console.error('Error saving push subscription:', error)
      return false
    }

    console.log('Successfully subscribed to push notifications')
    return true

  } catch (error) {
    console.error('Error subscribing to push notifications:', error)
    return false
  }
}

// Unsubscribe from push notifications
export async function unsubscribeFromPush(userId: string): Promise<boolean> {
  try {
    const registration = await navigator.serviceWorker.getRegistration()
    if (!registration) return true

    const subscription = await registration.pushManager.getSubscription()
    if (!subscription) return true

    // Unsubscribe from push manager
    await subscription.unsubscribe()

    // Remove from database
    const supabase = createSupabaseClient()
    const { error } = await supabase
      .from('push_subscriptions')
      .delete()
      .eq('user_id', userId)

    if (error) {
      console.error('Error removing push subscription:', error)
      return false
    }

    console.log('Successfully unsubscribed from push notifications')
    return true

  } catch (error) {
    console.error('Error unsubscribing from push notifications:', error)
    return false
  }
}

// Check if user is subscribed
export async function isSubscribedToPush(): Promise<boolean> {
  try {
    const registration = await navigator.serviceWorker.getRegistration()
    if (!registration) return false

    const subscription = await registration.pushManager.getSubscription()
    return !!subscription

  } catch (error) {
    console.error('Error checking push subscription:', error)
    return false
  }
}

// Utility function to convert VAPID key
function urlBase64ToUint8Array(base64String: string): Uint8Array {
  if (!base64String) {
    throw new Error('VAPID key is required')
  }

  const padding = '='.repeat((4 - base64String.length % 4) % 4)
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/')

  const rawData = window.atob(base64)
  const outputArray = new Uint8Array(rawData.length)

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i)
  }
  return outputArray
}

// Show a test notification
export async function showTestNotification(): Promise<void> {
  if (!('Notification' in window)) {
    throw new Error('This browser does not support notifications')
  }

  const permission = await requestNotificationPermission()
  if (permission === 'granted') {
    new Notification('OnlyDiary Test', {
      body: 'Push notifications are working! 🎉',
      icon: '/icon-192x192.png'
    })
  }
}

// Get user's notification preferences
export async function getNotificationPreferences(userId: string): Promise<{
  comments: boolean
  replies: boolean
  waitlist: boolean
}> {
  const supabase = createSupabaseClient()

  const { data, error } = await supabase
    .from('users')
    .select('notification_preferences')
    .eq('id', userId)
    .single()

  if (error || !data?.notification_preferences) {
    // Default preferences
    return {
      comments: true,
      replies: true,
      waitlist: true
    }
  }

  return data.notification_preferences
}

// Update user's notification preferences
export async function updateNotificationPreferences(
  userId: string,
  preferences: { comments: boolean; replies: boolean; waitlist: boolean }
): Promise<boolean> {
  const supabase = createSupabaseClient()

  const { error } = await supabase
    .from('users')
    .update({ notification_preferences: preferences })
    .eq('id', userId)

  return !error
}
