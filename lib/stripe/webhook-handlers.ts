// Add this to your existing webhook handler
import { createSupabaseClient } from '@/lib/supabase/client'

export async function handleNotificationCreditsPayment(session: any) {
  const supabase = createSupabaseClient()
  
  // Extract customer email from session
  const customerEmail = session.customer_details?.email
  if (!customerEmail) {
    console.error('No customer email in session')
    return
  }

  // Find user by email
  const { data: user, error: userError } = await supabase
    .from('users')
    .select('id, notification_credits')
    .eq('email', customerEmail)
    .single()

  if (userError || !user) {
    console.error('User not found for email:', customerEmail)
    return
  }

  // Calculate credits based on amount paid
  // $0.05 per credit = 5 cents per credit
  const amountPaid = session.amount_total // in cents
  const creditsToAdd = Math.floor(amountPaid / 5) // 5 cents per credit

  if (creditsToAdd <= 0) {
    console.error('Invalid payment amount:', amountPaid)
    return
  }

  // Add credits to user account
  const { error: updateError } = await supabase
    .from('users')
    .update({
      notification_credits: (user.notification_credits || 0) + creditsToAdd
    })
    .eq('id', user.id)

  if (updateError) {
    console.error('Error updating user credits:', updateError)
    return
  }

  console.log(`Added ${creditsToAdd} credits to user ${user.id}`)

  // Optional: Send confirmation email
  // await sendCreditsConfirmationEmail(customerEmail, creditsToAdd)
}
