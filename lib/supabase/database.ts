import { createSupabaseClient } from './client'

// Enhanced database operations with retry logic for critical operations
const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2
}

const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms))

const getRetryDelay = (attempt: number): number => {
  const delay = DEFAULT_RETRY_CONFIG.baseDelay * Math.pow(DEFAULT_RETRY_CONFIG.backoffMultiplier, attempt - 1)
  return Math.min(delay, DEFAULT_RETRY_CONFIG.maxDelay)
}

const withDatabaseRetry = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  maxRetries: number = DEFAULT_RETRY_CONFIG.maxRetries
): Promise<T> => {
  let lastError: any
  
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      const result = await operation()
      
      if (attempt > 1) {
        console.log(`✅ ${operationName} succeeded on attempt ${attempt}`)
      }
      
      return result
    } catch (error) {
      lastError = error
      
      // Don't retry on certain errors
      if (error?.code === 'PGRST116' || // Row not found
          error?.code === 'PGRST204' || // No content
          error?.message?.includes('duplicate key') ||
          error?.message?.includes('violates check constraint') ||
          error?.status === 400 ||
          error?.status === 401 ||
          error?.status === 403) {
        throw error
      }
      
      if (attempt > maxRetries) {
        console.error(`❌ ${operationName} failed after ${maxRetries} retries:`, error)
        throw error
      }
      
      const delay = getRetryDelay(attempt)
      console.warn(`⚠️ ${operationName} failed (attempt ${attempt}/${maxRetries}). Retrying in ${delay}ms...`)
      
      await sleep(delay)
    }
  }
  
  throw lastError
}

// Enhanced database utilities for critical operations
export const enhancedDatabase = {
  // Enhanced select with retry for critical reads
  selectWithRetry: async <T>(
    table: string,
    query: string = '*',
    filters?: Record<string, any>
  ): Promise<T> => {
    return withDatabaseRetry(async () => {
      const supabase = createSupabaseClient()
      let queryBuilder = supabase.from(table).select(query)
      
      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          queryBuilder = queryBuilder.eq(key, value)
        })
      }
      
      const { data, error } = await queryBuilder
      if (error) throw error
      return data as T
    }, `SELECT from ${table}`)
  },

  // Enhanced single row select
  selectSingleWithRetry: async <T>(
    table: string,
    query: string = '*',
    filters: Record<string, any>
  ): Promise<T> => {
    return withDatabaseRetry(async () => {
      const supabase = createSupabaseClient()
      let queryBuilder = supabase.from(table).select(query)
      
      Object.entries(filters).forEach(([key, value]) => {
        queryBuilder = queryBuilder.eq(key, value)
      })
      
      const { data, error } = await queryBuilder.single()
      if (error) throw error
      return data as T
    }, `SELECT single from ${table}`)
  },

  // Enhanced insert with retry (fewer retries for writes)
  insertWithRetry: async <T>(
    table: string,
    data: any
  ): Promise<T> => {
    return withDatabaseRetry(async () => {
      const supabase = createSupabaseClient()
      const { data: result, error } = await supabase
        .from(table)
        .insert(data)
        .select()
        .single()
      
      if (error) throw error
      return result as T
    }, `INSERT into ${table}`, 2) // Fewer retries for writes
  },

  // Enhanced update with retry
  updateWithRetry: async <T>(
    table: string,
    data: any,
    filters: Record<string, any>
  ): Promise<T> => {
    return withDatabaseRetry(async () => {
      const supabase = createSupabaseClient()
      let queryBuilder = supabase.from(table).update(data)
      
      Object.entries(filters).forEach(([key, value]) => {
        queryBuilder = queryBuilder.eq(key, value)
      })
      
      const { data: result, error } = await queryBuilder.select().single()
      if (error) throw error
      return result as T
    }, `UPDATE ${table}`, 2)
  }
}

// Convenience functions for common operations
export const getUserProfile = (userId: string) =>
  enhancedDatabase.selectSingleWithRetry('users', '*', { id: userId })

export const getProject = (projectId: string, userId: string) =>
  enhancedDatabase.selectSingleWithRetry('projects', '*', { id: projectId, user_id: userId })

export const getChapter = (chapterId: string, userId: string) =>
  enhancedDatabase.selectSingleWithRetry('chapters', '*', { id: chapterId, user_id: userId })

export const getProjectChapters = (projectId: string) =>
  enhancedDatabase.selectWithRetry('chapters', '*', { project_id: projectId })

// For auto-save operations that need reliability
export const updateChapterWithRetry = (chapterId: string, data: any) =>
  enhancedDatabase.updateWithRetry('chapters', data, { id: chapterId })

export const updateProjectWithRetry = (projectId: string, data: any) =>
  enhancedDatabase.updateWithRetry('projects', data, { id: projectId })
