// Enhanced error handling utilities

export interface AppError {
  message: string
  type: 'network' | 'auth' | 'validation' | 'server' | 'unknown'
  retryable: boolean
  userMessage: string
}

export const parseSupabaseError = (error: any): AppError => {
  // Network errors
  if (!navigator.onLine) {
    return {
      message: error?.message || 'No internet connection',
      type: 'network',
      retryable: true,
      userMessage: 'You appear to be offline. Please check your internet connection and try again.'
    }
  }

  if (error?.message?.includes('Failed to fetch') || error?.message?.includes('NetworkError')) {
    return {
      message: error.message,
      type: 'network',
      retryable: true,
      userMessage: 'Connection issue detected. We\'re automatically retrying...'
    }
  }

  // Auth errors
  if (error?.message?.includes('JWT') || error?.message?.includes('token') || error?.message?.includes('session')) {
    return {
      message: error.message,
      type: 'auth',
      retryable: false,
      userMessage: 'Your session has expired. Please refresh the page to continue.'
    }
  }

  if (error?.message?.includes('Invalid login credentials')) {
    return {
      message: error.message,
      type: 'auth',
      retryable: false,
      userMessage: 'Invalid email or password. Please check your credentials and try again.'
    }
  }

  // Database errors
  if (error?.code === 'PGRST301') {
    return {
      message: error.message,
      type: 'server',
      retryable: true,
      userMessage: 'Database temporarily unavailable. We\'re working to restore service.'
    }
  }

  if (error?.code === 'PGRST116') {
    return {
      message: error.message,
      type: 'validation',
      retryable: false,
      userMessage: 'The requested item could not be found.'
    }
  }

  if (error?.message?.includes('duplicate key')) {
    return {
      message: error.message,
      type: 'validation',
      retryable: false,
      userMessage: 'This item already exists. Please try a different name or value.'
    }
  }

  if (error?.message?.includes('violates check constraint')) {
    return {
      message: error.message,
      type: 'validation',
      retryable: false,
      userMessage: 'Invalid data provided. Please check your input and try again.'
    }
  }

  // Storage errors
  if (error?.message?.includes('storage')) {
    return {
      message: error.message,
      type: 'server',
      retryable: true,
      userMessage: 'File upload service temporarily unavailable. Please try again in a moment.'
    }
  }

  // Generic server errors
  if (error?.status >= 500) {
    return {
      message: error.message,
      type: 'server',
      retryable: true,
      userMessage: 'Server error occurred. We\'re automatically retrying...'
    }
  }

  // Unknown errors
  return {
    message: error?.message || 'Unknown error',
    type: 'unknown',
    retryable: false,
    userMessage: error?.message || 'An unexpected error occurred. Please try again.'
  }
}

// Toast notification system
export const showErrorToast = (error: any, customMessage?: string) => {
  const appError = parseSupabaseError(error)
  const message = customMessage || appError.userMessage
  
  // You can integrate with your preferred toast library here
  // For now, we'll use a simple console log and alert
  console.error('App Error:', appError)
  
  if (appError.type === 'network' && appError.retryable) {
    // Don't show intrusive alerts for retryable network errors
    console.warn('Network error (auto-retrying):', message)
  } else {
    // Show user-friendly message for non-retryable errors
    alert(message)
  }
}

// Utility to check if an error should trigger a retry
export const shouldRetryError = (error: any): boolean => {
  const appError = parseSupabaseError(error)
  return appError.retryable
}

// Utility to get user-friendly error message
export const getUserErrorMessage = (error: any): string => {
  const appError = parseSupabaseError(error)
  return appError.userMessage
}
