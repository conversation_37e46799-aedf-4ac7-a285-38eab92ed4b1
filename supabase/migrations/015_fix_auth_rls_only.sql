-- Fix RLS performance issues by optimizing auth function calls ONLY
-- Replace auth.uid() with (select auth.uid()) to prevent re-evaluation for each row
-- This migration only touches policies that have auth performance warnings

-- Fix diary_entries policies
DROP POLICY IF EXISTS "Writers can manage their own entries" ON diary_entries;
CREATE POLICY "Writers can manage their own entries" ON diary_entries
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "<PERSON><PERSON> can view all entries" ON diary_entries;
CREATE POLICY "Ad<PERSON> can view all entries" ON diary_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix photos policies
DROP POLICY IF EXISTS "Writers can manage photos for their entries" ON photos;
CREATE POLICY "Writers can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = (select auth.uid())
        )
    );

DROP POLICY IF EXISTS "Ad<PERSON> can view all photos" ON photos;
CREATE POLICY "Ad<PERSON> can view all photos" ON photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix payments policies
DROP POLICY IF EXISTS "Users can view their payment history" ON payments;
CREATE POLICY "Users can view their payment history" ON payments
    FOR SELECT USING (
        (select auth.uid()) = payer_id OR (select auth.uid()) = writer_id
    );

DROP POLICY IF EXISTS "Admins can view all payments" ON payments;
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix post_reads policies
DROP POLICY IF EXISTS "Users can create read records" ON post_reads;
CREATE POLICY "Users can create read records" ON post_reads
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can view reads of their content" ON post_reads;
CREATE POLICY "Writers can view reads of their content" ON post_reads
    FOR SELECT USING ((select auth.uid()) = writer_id);

-- Fix loves policies (only if table exists)
DROP POLICY IF EXISTS "Users can manage their own loves" ON loves;
CREATE POLICY "Users can manage their own loves" ON loves
    FOR ALL USING ((select auth.uid()) = user_id);

-- Fix projects policies
DROP POLICY IF EXISTS "Users can manage their own projects" ON projects;
CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING ((select auth.uid()) = user_id);

-- Fix chapters policies
DROP POLICY IF EXISTS "Users can view published chapters or own chapters" ON chapters;
CREATE POLICY "Users can view published chapters or own chapters" ON chapters
    FOR SELECT USING (
        is_published = true OR (select auth.uid()) = user_id
    );

-- Fix other chapters policies that use auth.uid()
DROP POLICY IF EXISTS "Writers can manage their own chapters" ON chapters;
CREATE POLICY "Writers can manage their own chapters" ON chapters
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Admins can view all chapters" ON chapters;
CREATE POLICY "Admins can view all chapters" ON chapters
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Fix users policies
DROP POLICY IF EXISTS "Users can insert their own profile" ON users;
CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK ((select auth.uid()) = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON users;
CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING ((select auth.uid()) = id);
