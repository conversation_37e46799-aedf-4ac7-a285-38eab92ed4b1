-- Fix function search path security warnings and RLS performance issues
-- This migration sets search_path = '' for all functions to prevent SQL injection attacks
-- and optimizes RLS policies to improve query performance at scale

-- PART 1: Fix function search path security warnings
-- Functions without parameters
ALTER FUNCTION public.check_photo_limit() SET search_path = '';
ALTER FUNCTION public.notify_comment_created() SET search_path = '';
ALTER FUNCTION public.update_bookmark_count() SET search_path = '';
ALTER FUNCTION public.update_entry_count() SET search_path = '';
ALTER FUNCTION public.update_flower_count() SET search_path = '';
ALTER FUNCTION public.update_love_count() SET search_path = '';
ALTER FUNCTION public.update_hourly_love_stats() SET search_path = '';
ALTER FUNCTION public.update_post_credits_updated_at() SET search_path = '';
ALTER FUNCTION public.update_subscriber_count() SET search_path = '';
ALTER FUNCTION public.update_updated_at_column() SET search_path = '';

-- Functions with single UUID parameter
ALTER FUNCTION public.get_follower_count(p_writer_id uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_locked_entries(writer_uuid uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_public_data(writer_uuid uuid) SET search_path = '';
ALTER FUNCTION public.is_following(p_writer_id uuid) SET search_path = '';

-- Functions with integer parameter
ALTER FUNCTION public.get_top_diary_entries_hourly(limit_count integer) SET search_path = '';

-- Functions with two UUID parameters
ALTER FUNCTION public.consume_credit_for_post(reader_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.get_entry_preview(entry_id uuid, viewer_id uuid) SET search_path = '';
ALTER FUNCTION public.user_can_comment_on_entry(user_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.user_can_read_post(reader_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.user_has_active_subscription(subscriber_uuid uuid, writer_uuid uuid) SET search_path = '';

-- PART 2: Fix RLS performance issues by optimizing auth function calls
-- Replace auth.uid() with (select auth.uid()) to prevent re-evaluation for each row

-- Drop and recreate diary_entries policies with optimized auth calls
DROP POLICY IF EXISTS "Writers can manage their own entries" ON diary_entries;
CREATE POLICY "Writers can manage their own entries" ON diary_entries
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Admins can view all entries" ON diary_entries;
CREATE POLICY "Admins can view all entries" ON diary_entries
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Drop and recreate photos policies with optimized auth calls
DROP POLICY IF EXISTS "Writers can manage photos for their entries" ON photos;
CREATE POLICY "Writers can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM diary_entries 
            WHERE id = diary_entry_id AND user_id = (select auth.uid())
        )
    );

DROP POLICY IF EXISTS "Admins can view all photos" ON photos;
CREATE POLICY "Admins can view all photos" ON photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Drop and recreate payments policies with optimized auth calls
DROP POLICY IF EXISTS "Users can view their payment history" ON payments;
CREATE POLICY "Users can view their payment history" ON payments
    FOR SELECT USING (
        (select auth.uid()) = payer_id OR (select auth.uid()) = writer_id
    );

DROP POLICY IF EXISTS "Admins can view all payments" ON payments;
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = (select auth.uid()) AND role = 'admin'
        )
    );

-- Drop and recreate post_reads policies with optimized auth calls
DROP POLICY IF EXISTS "Users can create read records" ON post_reads;
CREATE POLICY "Users can create read records" ON post_reads
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

-- Drop and recreate flags policies with optimized auth calls
DROP POLICY IF EXISTS "Authenticated users can create flags" ON flags;
CREATE POLICY "Authenticated users can create flags" ON flags
    FOR INSERT WITH CHECK ((select auth.uid()) = reporter_id);

-- Drop and recreate projects policies with optimized auth calls
DROP POLICY IF EXISTS "Users can view published projects or own projects" ON projects;
CREATE POLICY "Users can view published projects or own projects" ON projects
    FOR SELECT USING (
        NOT is_private OR (select auth.uid()) = user_id
    );

-- Create loves table if it doesn't exist (referenced in performance warnings but missing from schema)
CREATE TABLE IF NOT EXISTS loves (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    diary_entry_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, diary_entry_id)
);

-- Add indexes for loves table
CREATE INDEX IF NOT EXISTS idx_loves_user_id ON loves(user_id);
CREATE INDEX IF NOT EXISTS idx_loves_diary_entry_id ON loves(diary_entry_id);
CREATE INDEX IF NOT EXISTS idx_loves_created_at ON loves(created_at);

-- Enable RLS on loves table
ALTER TABLE loves ENABLE ROW LEVEL SECURITY;

-- Create optimized RLS policies for loves table
CREATE POLICY "Users can manage their own loves" ON loves
    FOR ALL USING ((select auth.uid()) = user_id);
