-- SAFE COMPREHENSIVE RLS OPTIMIZATION
-- This migration fixes ALL RLS performance issues systematically
-- Approach: Fix auth optimization first, then address multiple permissive policies

-- =============================================================================
-- PART 1: FIX ALL AUTH FUNCTION RE-EVALUATION ISSUES
-- =============================================================================

-- Users table policies
DROP POLICY IF EXISTS "Users can insert their own profile" ON users;
CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK ((select auth.uid()) = id);

DROP POLICY IF EXISTS "Users can update their own profile" ON users;
CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING ((select auth.uid()) = id);

-- Diary entries policies
DROP POLICY IF EXISTS "Writers can manage their own entries" ON diary_entries;
CREATE POLICY "Writers can manage their own entries" ON diary_entries
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Admins can view all entries" ON diary_entries;
CREATE POLICY "Admins can view all entries" ON diary_entries
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- Photos policies
DROP POLICY IF EXISTS "Writers can manage photos for their entries" ON photos;
CREATE POLICY "Writers can manage photos for their entries" ON photos
    FOR ALL USING (
        EXISTS (SELECT 1 FROM diary_entries WHERE id = diary_entry_id AND user_id = (select auth.uid()))
    );

DROP POLICY IF EXISTS "Admins can view all photos" ON photos;
CREATE POLICY "Admins can view all photos" ON photos
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- Payments policies
DROP POLICY IF EXISTS "Users can view their payment history" ON payments;
CREATE POLICY "Users can view their payment history" ON payments
    FOR SELECT USING ((select auth.uid()) = payer_id OR (select auth.uid()) = writer_id);

DROP POLICY IF EXISTS "Admins can view all payments" ON payments;
CREATE POLICY "Admins can view all payments" ON payments
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- Post reads policies
DROP POLICY IF EXISTS "Users can create read records" ON post_reads;
CREATE POLICY "Users can create read records" ON post_reads
    FOR INSERT WITH CHECK ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can view reads of their content" ON post_reads;
CREATE POLICY "Writers can view reads of their content" ON post_reads
    FOR SELECT USING ((select auth.uid()) = writer_id);

-- Loves policies
DROP POLICY IF EXISTS "Users can manage their own loves" ON loves;
CREATE POLICY "Users can manage their own loves" ON loves
    FOR ALL USING ((select auth.uid()) = user_id);

-- Projects policies
DROP POLICY IF EXISTS "Users can manage their own projects" ON projects;
CREATE POLICY "Users can manage their own projects" ON projects
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can manage their own projects" ON projects;
CREATE POLICY "Writers can manage their own projects" ON projects
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Admins can view all projects" ON projects;
CREATE POLICY "Admins can view all projects" ON projects
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- Chapters policies
DROP POLICY IF EXISTS "Users can view published chapters or own chapters" ON chapters;
CREATE POLICY "Users can view published chapters or own chapters" ON chapters
    FOR SELECT USING (is_published = true OR (select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can manage their own chapters" ON chapters;
CREATE POLICY "Writers can manage their own chapters" ON chapters
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Users can manage their own chapters" ON chapters;
CREATE POLICY "Users can manage their own chapters" ON chapters
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Admins can view all chapters" ON chapters;
CREATE POLICY "Admins can view all chapters" ON chapters
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

DROP POLICY IF EXISTS "Anyone can view published chapters from public projects" ON chapters;
CREATE POLICY "Anyone can view published chapters from public projects" ON chapters
    FOR SELECT USING (
        is_published = true AND 
        EXISTS (SELECT 1 FROM projects WHERE id = chapters.project_id AND is_private = false)
    );

-- Comments policies
DROP POLICY IF EXISTS "Admins can moderate all comments" ON comments;
CREATE POLICY "Admins can moderate all comments" ON comments
    FOR ALL USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

DROP POLICY IF EXISTS "Users can update their own comments" ON comments;
CREATE POLICY "Users can update their own comments" ON comments
    FOR UPDATE USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can moderate comments on their entries" ON comments;
CREATE POLICY "Writers can moderate comments on their entries" ON comments
    FOR UPDATE USING (
        EXISTS (SELECT 1 FROM diary_entries WHERE id = diary_entry_id AND user_id = (select auth.uid()))
    );

DROP POLICY IF EXISTS "Subscribers can comment on entries they're subscribed to" ON comments;
CREATE POLICY "Subscribers can comment on entries they're subscribed to" ON comments
    FOR INSERT WITH CHECK (
        (select auth.uid()) = user_id AND
        EXISTS (
            SELECT 1 FROM subscriptions s
            JOIN diary_entries de ON de.user_id = s.writer_id
            WHERE s.reader_id = (select auth.uid())
            AND de.id = diary_entry_id
            AND (s.status = 'active' OR s.active_until > NOW())
        )
    );

-- Flags policies
DROP POLICY IF EXISTS "Authenticated users can create flags" ON flags;
CREATE POLICY "Authenticated users can create flags" ON flags
    FOR INSERT WITH CHECK ((select auth.uid()) = reporter_id);

DROP POLICY IF EXISTS "Users can view their own flags" ON flags;
CREATE POLICY "Users can view their own flags" ON flags
    FOR SELECT USING ((select auth.uid()) = reporter_id);

DROP POLICY IF EXISTS "Admins can view and manage all flags" ON flags;
CREATE POLICY "Admins can view and manage all flags" ON flags
    FOR ALL USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- Post credits policies
DROP POLICY IF EXISTS "Users can manage their own credits" ON post_credits;
CREATE POLICY "Users can manage their own credits" ON post_credits
    FOR ALL USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Users can view their own credits" ON post_credits;
CREATE POLICY "Users can view their own credits" ON post_credits
    FOR SELECT USING ((select auth.uid()) = user_id);

DROP POLICY IF EXISTS "Writers can view their credits" ON post_credits;
CREATE POLICY "Writers can view their credits" ON post_credits
    FOR SELECT USING ((select auth.uid()) = writer_id);

-- Subscriptions policies (handle both subscriber_id and reader_id column variations)
DROP POLICY IF EXISTS "Users can view their own subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Readers can manage their subscriptions" ON subscriptions;
DROP POLICY IF EXISTS "Writers can view their subscriptions" ON subscriptions;

-- Create policies that work with reader_id column (current schema)
CREATE POLICY "Users can view their own subscriptions" ON subscriptions
    FOR SELECT USING ((select auth.uid()) = reader_id OR (select auth.uid()) = writer_id);

DROP POLICY IF EXISTS "Users can create subscriptions as subscriber" ON subscriptions;
CREATE POLICY "Users can create subscriptions as subscriber" ON subscriptions
    FOR INSERT WITH CHECK ((select auth.uid()) = reader_id);

DROP POLICY IF EXISTS "Writers can view their subscribers" ON subscriptions;
CREATE POLICY "Writers can view their subscribers" ON subscriptions
    FOR SELECT USING ((select auth.uid()) = writer_id);

DROP POLICY IF EXISTS "Admins can view all subscriptions" ON subscriptions;
CREATE POLICY "Admins can view all subscriptions" ON subscriptions
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );

-- =============================================================================
-- PART 2: CONSOLIDATE MULTIPLE PERMISSIVE POLICIES
-- =============================================================================

-- Bookmarks table - consolidate multiple permissive policies
DROP POLICY IF EXISTS "Creators can view their bookmarks" ON bookmarks;
DROP POLICY IF EXISTS "Readers can manage their bookmarks" ON bookmarks;
DROP POLICY IF EXISTS "Users can manage bookmarks" ON bookmarks;

CREATE POLICY "Users can manage bookmarks" ON bookmarks
    FOR ALL USING ((select auth.uid()) = reader_id OR (select auth.uid()) = creator_id);

-- Follows table - consolidate if exists
DROP POLICY IF EXISTS "Users can manage their own follows" ON follows;
DROP POLICY IF EXISTS "Writers can view their followers" ON follows;

CREATE POLICY "Users can manage follows" ON follows
    FOR ALL USING ((select auth.uid()) = follower_id OR (select auth.uid()) = writer_id);

-- Flowers table - consolidate if exists
DROP POLICY IF EXISTS "Users can view their flowers" ON flowers;
DROP POLICY IF EXISTS "Users can give flowers" ON flowers;
DROP POLICY IF EXISTS "Users can delete flowers they gave" ON flowers;

CREATE POLICY "Users can manage flowers" ON flowers
    FOR ALL USING ((select auth.uid()) = receiver_id OR (select auth.uid()) = giver_id);

-- Settings policies
DROP POLICY IF EXISTS "Only admins can modify settings" ON settings;
CREATE POLICY "Only admins can modify settings" ON settings
    FOR ALL USING (
        EXISTS (SELECT 1 FROM users WHERE id = (select auth.uid()) AND role = 'admin')
    );
