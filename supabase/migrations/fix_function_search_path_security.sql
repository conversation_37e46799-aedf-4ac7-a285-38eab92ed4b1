-- Fix function search path security warnings
-- This migration sets search_path = '' for all functions to prevent SQL injection attacks

-- Functions without parameters
ALTER FUNCTION public.check_photo_limit() SET search_path = '';
ALTER FUNCTION public.notify_comment_created() SET search_path = '';
ALTER FUNCTION public.update_bookmark_count() SET search_path = '';
ALTER FUNCTION public.update_entry_count() SET search_path = '';
ALTER FUNCTION public.update_flower_count() SET search_path = '';
ALTER FUNCTION public.update_love_count() SET search_path = '';
ALTER FUNCTION public.update_hourly_love_stats() SET search_path = '';
ALTER FUNCTION public.update_post_credits_updated_at() SET search_path = '';
ALTER FUNCTION public.update_subscriber_count() SET search_path = '';
ALTER FUNCTION public.update_updated_at_column() SET search_path = '';

-- Functions with single UUID parameter
ALTER FUNCTION public.get_follower_count(p_writer_id uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_locked_entries(writer_uuid uuid) SET search_path = '';
ALTER FUNCTION public.get_writer_public_data(writer_uuid uuid) SET search_path = '';
ALTER FUNCTION public.is_following(p_writer_id uuid) SET search_path = '';

-- Functions with integer parameter
ALTER FUNCTION public.get_top_diary_entries_hourly(limit_count integer) SET search_path = '';

-- Functions with two UUID parameters
ALTER FUNCTION public.consume_credit_for_post(reader_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.get_entry_preview(entry_id uuid, viewer_id uuid) SET search_path = '';
ALTER FUNCTION public.user_can_comment_on_entry(user_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.user_can_read_post(reader_uuid uuid, entry_uuid uuid) SET search_path = '';
ALTER FUNCTION public.user_has_active_subscription(subscriber_uuid uuid, writer_uuid uuid) SET search_path = '';
